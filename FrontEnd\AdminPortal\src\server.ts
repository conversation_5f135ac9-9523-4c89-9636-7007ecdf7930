import {
  AngularNodeAppEng<PERSON>,
  createNodeRequest<PERSON><PERSON><PERSON>,
  isMainModule,
  writeResponseToNodeResponse,
} from '@angular/ssr/node';
import express from 'express';
import { join } from 'node:path';

const browserDistFolder = join(import.meta.dirname, '../browser');

const app = express();
const angularApp = new AngularNodeAppEngine();

/**
 * Example Express Rest API endpoints can be defined here.
 * Uncomment and define endpoints as necessary.
 *
 * Example:
 * ```ts
 * app.get('/api/{*splat}', (req, res) => {
 *   // Handle API request
 * });
 * ```
 */

/**
 * Serve static files from /browser
 */
app.use(
  express.static(browserDistFolder, {
    maxAge: '1y',
    index: false,
    redirect: false,
  }),
);

/**
 * Handle all other requests by rendering the Angular application.
 */
app.use((req, res, next) => {
  angularApp
    .handle(req)
    .then((response) =>
      response ? writeResponseToNodeResponse(response, res) : next(),
    )
    .catch(next);
});

/**
 * Start the server if this module is the main entry point.
 * The server listens on the port defined by the `PORT` environment variable, or defaults based on environment.
 */
if (isMainModule(import.meta.url)) {
  // Default ports based on environment
  const defaultPorts = {
    'production': 3000,
    'stage': 4202,
    'dev': 4201,
    'local': 4200
  };

  const nodeEnv = process.env['NODE_ENV'] || 'production';
  const defaultPort = defaultPorts[nodeEnv as keyof typeof defaultPorts] || 3000;
  const port = Number(process.env['PORT']) || defaultPort;

  app.listen(port,'0.0.0.0', (error) => {
    if (error) {
      throw error;
    }

    console.log(`Node Express server listening on http://localhost:${port} (${nodeEnv} environment)`);
  });
}

/**
 * Request handler used by the Angular CLI (for dev-server and during build) or Firebase Cloud Functions.
 */
export const reqHandler = createNodeRequestHandler(app);
