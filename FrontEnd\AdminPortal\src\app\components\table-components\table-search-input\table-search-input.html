<mat-form-field appearance="outline" [class]="'table-search-input rounded-full !w-full '+buttonClasses()">
  <input matInput [type]="type()" [placeholder]="placeholder()" [value]="currentValue()" [name]="name()"
    [required]="required()" [disabled]="disabled()" [autocomplete]="autocomplete()" (input)="onInputChange($event)">
  @if (iconName()) {
  <mat-icon class="material-symbols-outlined" matSuffix>{{ iconName() }}</mat-icon>
  }
</mat-form-field>