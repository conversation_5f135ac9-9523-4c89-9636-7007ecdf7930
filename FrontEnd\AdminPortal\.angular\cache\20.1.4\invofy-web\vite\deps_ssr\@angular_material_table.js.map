{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/table.mjs", "../../../../../../node_modules/@angular/material/fesm2022/table.mjs"], "sourcesContent": ["import { i as isDataSource } from './data-source-D34wiQZj.mjs';\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction CdkTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction CdkTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 0);\n    i0.ɵɵelementContainer(3, 2)(4, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CdkTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 2)(2, 3)(3, 4);\n  }\n}\nfunction CdkTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction CdkTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nexport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, booleanAttribute, Input, ContentChild, ElementRef, IterableDiffers, ViewContainerRef, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ChangeDetectorRef, DOCUMENT, EventEmitter, Injector, HostAttributeToken, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { b as _VIEW_REPEATER_STRATEGY, _ as _RecycleViewRepeaterStrategy, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-Cvpav0PR.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport '@angular/common';\nimport './element-x4z00URv.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function CdkCellDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCellDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCellDef,\n    selectors: [[\"\", \"cdkCellDef\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function CdkHeaderCellDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderCellDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkHeaderCellDef,\n    selectors: [[\"\", \"cdkHeaderCellDef\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function CdkFooterCellDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterCellDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFooterCellDef,\n    selectors: [[\"\", \"cdkFooterCellDef\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  _hasStickyChanged = false;\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  _name;\n  /** Whether the cell is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _sticky = false;\n  /**\n   * Whether this column should be sticky positioned on the end of the row. Should make sure\n   * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n   * has been changed.\n   */\n  get stickyEnd() {\n    return this._stickyEnd;\n  }\n  set stickyEnd(value) {\n    if (value !== this._stickyEnd) {\n      this._stickyEnd = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _stickyEnd = false;\n  /** @docs-private */\n  cell;\n  /** @docs-private */\n  headerCell;\n  /** @docs-private */\n  footerCell;\n  /**\n   * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n   * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n   * do not match are replaced by the '-' character.\n   */\n  cssClassFriendlyName;\n  /**\n   * Class name for cells in this column.\n   * @docs-private\n   */\n  _columnCssClassName;\n  constructor() {}\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  /**\n   * Overridable method that sets the css classes that will be added to every cell in this\n   * column.\n   * In the future, columnCssClassName will change from type string[] to string and this\n   * will set a single string value.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setNameInput(value) {\n    // If the directive is set without a name (updated programmatically), then this setter will\n    // trigger with an empty string and should not overwrite the programmatically set value.\n    if (value) {\n      this._name = value;\n      this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n      this._updateColumnCssClassName();\n    }\n  }\n  static ɵfac = function CdkColumnDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkColumnDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkColumnDef,\n    selectors: [[\"\", \"cdkColumnDef\", \"\"]],\n    contentQueries: function CdkColumnDef_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CdkCellDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkHeaderCellDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkFooterCellDef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerCell = _t.first);\n      }\n    },\n    inputs: {\n      name: [0, \"cdkColumnDef\", \"name\"],\n      sticky: [2, \"sticky\", \"sticky\", booleanAttribute],\n      stickyEnd: [2, \"stickyEnd\", \"stickyEnd\", booleanAttribute]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n      useExisting: CdkColumnDef\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkColumnDef]',\n      providers: [{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }]\n    }]\n  }], () => [], {\n    name: [{\n      type: Input,\n      args: ['cdkColumnDef']\n    }],\n    sticky: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stickyEnd: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    cell: [{\n      type: ContentChild,\n      args: [CdkCellDef]\n    }],\n    headerCell: [{\n      type: ContentChild,\n      args: [CdkHeaderCellDef]\n    }],\n    footerCell: [{\n      type: ContentChild,\n      args: [CdkFooterCellDef]\n    }]\n  });\n})();\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n  constructor() {\n    super(inject(CdkColumnDef), inject(ElementRef));\n  }\n  static ɵfac = function CdkHeaderCell_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderCell)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkHeaderCell,\n    selectors: [[\"cdk-header-cell\"], [\"th\", \"cdk-header-cell\", \"\"]],\n    hostAttrs: [\"role\", \"columnheader\", 1, \"cdk-header-cell\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-header-cell, th[cdk-header-cell]',\n      host: {\n        'class': 'cdk-header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n  constructor() {\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n    super(columnDef, elementRef);\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static ɵfac = function CdkFooterCell_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterCell)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFooterCell,\n    selectors: [[\"cdk-footer-cell\"], [\"td\", \"cdk-footer-cell\", \"\"]],\n    hostAttrs: [1, \"cdk-footer-cell\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n      host: {\n        'class': 'cdk-footer-cell'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n  constructor() {\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n    super(columnDef, elementRef);\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static ɵfac = function CdkCell_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCell)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCell,\n    selectors: [[\"cdk-cell\"], [\"td\", \"cdk-cell\", \"\"]],\n    hostAttrs: [1, \"cdk-cell\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-cell, td[cdk-cell]',\n      host: {\n        'class': 'cdk-cell'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n  template = inject(TemplateRef);\n  _differs = inject(IterableDiffers);\n  /** The columns to be displayed on this row. */\n  columns;\n  /** Differ used to check if any changes were made to the columns. */\n  _columnsDiffer;\n  constructor() {}\n  ngOnChanges(changes) {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    if (!this._columnsDiffer) {\n      const columns = changes['columns'] && changes['columns'].currentValue || [];\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff() {\n    return this._columnsDiffer.diff(this.columns);\n  }\n  /** Gets this row def's relevant cell template from the provided column def. */\n  extractCellTemplate(column) {\n    if (this instanceof CdkHeaderRowDef) {\n      return column.headerCell.template;\n    }\n    if (this instanceof CdkFooterRowDef) {\n      return column.footerCell.template;\n    } else {\n      return column.cell.template;\n    }\n  }\n  static ɵfac = function BaseRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseRowDef,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseRowDef, [{\n    type: Directive\n  }], () => [], null);\n})();\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  _hasStickyChanged = false;\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _sticky = false;\n  constructor() {\n    super(inject(TemplateRef), inject(IterableDiffers));\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  static ɵfac = function CdkHeaderRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkHeaderRowDef,\n    selectors: [[\"\", \"cdkHeaderRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"cdkHeaderRowDef\", \"columns\"],\n      sticky: [2, \"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkHeaderRowDef'\n      }]\n    }]\n  }], () => [], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkHeaderRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  _hasStickyChanged = false;\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _sticky = false;\n  constructor() {\n    super(inject(TemplateRef), inject(IterableDiffers));\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  static ɵfac = function CdkFooterRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFooterRowDef,\n    selectors: [[\"\", \"cdkFooterRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"cdkFooterRowDef\", \"columns\"],\n      sticky: [2, \"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkFooterRowDef'\n      }]\n    }]\n  }], () => [], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkFooterRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  /**\n   * Function that should return true if this row template should be used for the provided index\n   * and row data. If left undefined, this row will be considered the default row template to use\n   * when no other when functions return true for the data.\n   * For every row, there must be at least one when function that passes or an undefined to default.\n   */\n  when;\n  constructor() {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    super(inject(TemplateRef), inject(IterableDiffers));\n  }\n  static ɵfac = function CdkRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkRowDef,\n    selectors: [[\"\", \"cdkRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"cdkRowDefColumns\", \"columns\"],\n      when: [0, \"cdkRowDefWhen\", \"when\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'cdkRowDefWhen'\n      }]\n    }]\n  }], () => [], null);\n})();\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n  _viewContainer = inject(ViewContainerRef);\n  /** The ordered list of cells to render within this outlet's view container */\n  cells;\n  /** The data context to be provided to each cell */\n  context;\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static mostRecentCellOutlet = null;\n  constructor() {\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n  ngOnDestroy() {\n    // If this was the last outlet being rendered in the view, remove the reference\n    // from the static property after it has been destroyed to avoid leaking memory.\n    if (CdkCellOutlet.mostRecentCellOutlet === this) {\n      CdkCellOutlet.mostRecentCellOutlet = null;\n    }\n  }\n  static ɵfac = function CdkCellOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCellOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCellOutlet,\n    selectors: [[\"\", \"cdkCellOutlet\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellOutlet]'\n    }]\n  }], () => [], null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n  static ɵfac = function CdkHeaderRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkHeaderRow,\n    selectors: [[\"cdk-header-row\"], [\"tr\", \"cdk-header-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"cdk-header-row\"],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function CdkHeaderRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-header-row, tr[cdk-header-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n  static ɵfac = function CdkFooterRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkFooterRow,\n    selectors: [[\"cdk-footer-row\"], [\"tr\", \"cdk-footer-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"cdk-footer-row\"],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function CdkFooterRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-footer-row, tr[cdk-footer-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-footer-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n  static ɵfac = function CdkRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkRow,\n    selectors: [[\"cdk-row\"], [\"tr\", \"cdk-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"cdk-row\"],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function CdkRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-row, tr[cdk-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n  templateRef = inject(TemplateRef);\n  _contentClassName = 'cdk-no-data-row';\n  constructor() {}\n  static ɵfac = function CdkNoDataRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkNoDataRow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkNoDataRow,\n    selectors: [[\"ng-template\", \"cdkNoDataRow\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkNoDataRow]'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Directions that can be used when setting sticky positioning.\n * @docs-private\n */\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n  _isNativeHtmlTable;\n  _stickCellCss;\n  _isBrowser;\n  _needsPositionStickyOnElement;\n  direction;\n  _positionListener;\n  _tableInjector;\n  _elemSizeCache = new WeakMap();\n  _resizeObserver = globalThis?.ResizeObserver ? new globalThis.ResizeObserver(entries => this._updateCachedSizes(entries)) : null;\n  _updatedStickyColumnsParamsToReplay = [];\n  _stickyColumnsReplayTimeout = null;\n  _cachedCellWidths = [];\n  _borderCellCss;\n  _destroyed = false;\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   * @param _tableInjector The table's Injector.\n   */\n  constructor(_isNativeHtmlTable, _stickCellCss, _isBrowser = true, _needsPositionStickyOnElement = true, direction, _positionListener, _tableInjector) {\n    this._isNativeHtmlTable = _isNativeHtmlTable;\n    this._stickCellCss = _stickCellCss;\n    this._isBrowser = _isBrowser;\n    this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n    this.direction = direction;\n    this._positionListener = _positionListener;\n    this._tableInjector = _tableInjector;\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`\n    };\n  }\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows, stickyDirections) {\n    if (stickyDirections.includes('left') || stickyDirections.includes('right')) {\n      this._removeFromStickyColumnReplayQueue(rows);\n    }\n    const elementsToClear = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n      elementsToClear.push(row, ...Array.from(row.children));\n    }\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    afterNextRender({\n      write: () => {\n        for (const element of elementsToClear) {\n          this._removeStickyStyle(element, stickyDirections);\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   * @param replay Whether to enqueue this call for replay after a ResizeObserver update.\n   */\n  updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true, replay = true) {\n    // Don't cache any state if none of the columns are sticky.\n    if (!rows.length || !this._isBrowser || !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n      this._positionListener?.stickyColumnsUpdated({\n        sizes: []\n      });\n      this._positionListener?.stickyEndColumnsUpdated({\n        sizes: []\n      });\n      return;\n    }\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    const firstRow = rows[0];\n    const numCells = firstRow.children.length;\n    const isRtl = this.direction === 'rtl';\n    const start = isRtl ? 'right' : 'left';\n    const end = isRtl ? 'left' : 'right';\n    const lastStickyStart = stickyStartStates.lastIndexOf(true);\n    const firstStickyEnd = stickyEndStates.indexOf(true);\n    let cellWidths;\n    let startPositions;\n    let endPositions;\n    if (replay) {\n      this._updateStickyColumnReplayQueue({\n        rows: [...rows],\n        stickyStartStates: [...stickyStartStates],\n        stickyEndStates: [...stickyEndStates]\n      });\n    }\n    afterNextRender({\n      earlyRead: () => {\n        cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n        startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n        endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n      },\n      write: () => {\n        for (const row of rows) {\n          for (let i = 0; i < numCells; i++) {\n            const cell = row.children[i];\n            if (stickyStartStates[i]) {\n              this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n            }\n            if (stickyEndStates[i]) {\n              this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n            }\n          }\n        }\n        if (this._positionListener && cellWidths.some(w => !!w)) {\n          this._positionListener.stickyColumnsUpdated({\n            sizes: lastStickyStart === -1 ? [] : cellWidths.slice(0, lastStickyStart + 1).map((width, index) => stickyStartStates[index] ? width : null)\n          });\n          this._positionListener.stickyEndColumnsUpdated({\n            sizes: firstStickyEnd === -1 ? [] : cellWidths.slice(firstStickyEnd).map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null).reverse()\n          });\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick, stickyStates, position) {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n    // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n    // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n    // sticky states need to be reversed as well.\n    const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n    const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n    // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n    const stickyOffsets = [];\n    const stickyCellHeights = [];\n    const elementsToStick = [];\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    afterNextRender({\n      earlyRead: () => {\n        for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n          stickyOffsets[rowIndex] = stickyOffset;\n          const row = rows[rowIndex];\n          elementsToStick[rowIndex] = this._isNativeHtmlTable ? Array.from(row.children) : [row];\n          const height = this._retrieveElementSize(row).height;\n          stickyOffset += height;\n          stickyCellHeights[rowIndex] = height;\n        }\n      },\n      write: () => {\n        const borderedRowIndex = states.lastIndexOf(true);\n        for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n          const offset = stickyOffsets[rowIndex];\n          const isBorderedRowIndex = rowIndex === borderedRowIndex;\n          for (const element of elementsToStick[rowIndex]) {\n            this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n          }\n        }\n        if (position === 'top') {\n          this._positionListener?.stickyHeaderRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick\n          });\n        } else {\n          this._positionListener?.stickyFooterRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick\n          });\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement, stickyStates) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    afterNextRender({\n      write: () => {\n        const tfoot = tableElement.querySelector('tfoot');\n        if (tfoot) {\n          if (stickyStates.some(state => !state)) {\n            this._removeStickyStyle(tfoot, ['bottom']);\n          } else {\n            this._addStickyStyle(tfoot, 'bottom', 0, false);\n          }\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /** Triggered by the table's OnDestroy hook. */\n  destroy() {\n    if (this._stickyColumnsReplayTimeout) {\n      clearTimeout(this._stickyColumnsReplayTimeout);\n    }\n    this._resizeObserver?.disconnect();\n    this._destroyed = true;\n  }\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element, stickyDirections) {\n    if (!element.classList.contains(this._stickCellCss)) {\n      return;\n    }\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(element, dir, dirValue, isBorderElement) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element) {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1\n    };\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n    return zIndex ? `${zIndex}` : '';\n  }\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row, recalculateCellWidths = true) {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n    const cellWidths = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      const cell = firstRowCells[i];\n      cellWidths.push(this._retrieveElementSize(cell).width);\n    }\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Retreives the most recently observed size of the specified element from the cache, or\n   * meaures it directly if not yet cached.\n   */\n  _retrieveElementSize(element) {\n    const cachedSize = this._elemSizeCache.get(element);\n    if (cachedSize) {\n      return cachedSize;\n    }\n    const clientRect = element.getBoundingClientRect();\n    const size = {\n      width: clientRect.width,\n      height: clientRect.height\n    };\n    if (!this._resizeObserver) {\n      return size;\n    }\n    this._elemSizeCache.set(element, size);\n    this._resizeObserver.observe(element, {\n      box: 'border-box'\n    });\n    return size;\n  }\n  /**\n   * Conditionally enqueue the requested sticky update and clear previously queued updates\n   * for the same rows.\n   */\n  _updateStickyColumnReplayQueue(params) {\n    this._removeFromStickyColumnReplayQueue(params.rows);\n    // No need to replay if a flush is pending.\n    if (!this._stickyColumnsReplayTimeout) {\n      this._updatedStickyColumnsParamsToReplay.push(params);\n    }\n  }\n  /** Remove updates for the specified rows from the queue. */\n  _removeFromStickyColumnReplayQueue(rows) {\n    const rowsSet = new Set(rows);\n    for (const update of this._updatedStickyColumnsParamsToReplay) {\n      update.rows = update.rows.filter(row => !rowsSet.has(row));\n    }\n    this._updatedStickyColumnsParamsToReplay = this._updatedStickyColumnsParamsToReplay.filter(update => !!update.rows.length);\n  }\n  /** Update _elemSizeCache with the observed sizes. */\n  _updateCachedSizes(entries) {\n    let needsColumnUpdate = false;\n    for (const entry of entries) {\n      const newEntry = entry.borderBoxSize?.length ? {\n        width: entry.borderBoxSize[0].inlineSize,\n        height: entry.borderBoxSize[0].blockSize\n      } : {\n        width: entry.contentRect.width,\n        height: entry.contentRect.height\n      };\n      if (newEntry.width !== this._elemSizeCache.get(entry.target)?.width && isCell(entry.target)) {\n        needsColumnUpdate = true;\n      }\n      this._elemSizeCache.set(entry.target, newEntry);\n    }\n    if (needsColumnUpdate && this._updatedStickyColumnsParamsToReplay.length) {\n      if (this._stickyColumnsReplayTimeout) {\n        clearTimeout(this._stickyColumnsReplayTimeout);\n      }\n      this._stickyColumnsReplayTimeout = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        for (const update of this._updatedStickyColumnsParamsToReplay) {\n          this.updateStickyColumns(update.rows, update.stickyStartStates, update.stickyEndStates, true, false);\n        }\n        this._updatedStickyColumnsParamsToReplay = [];\n        this._stickyColumnsReplayTimeout = null;\n      }, 0);\n    }\n  }\n}\nfunction isCell(element) {\n  return ['cdk-cell', 'cdk-header-cell', 'cdk-footer-cell'].some(klass => element.classList.contains(klass));\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function. ` + 'Or set `multiTemplateDataRows`.');\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n  return Error(`Could not find a matching row definition for the ` + `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n  return Error('Missing definitions for header, footer, and row; ' + 'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n  static ɵfac = function CdkRecycleRows_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkRecycleRows)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkRecycleRows,\n    selectors: [[\"cdk-table\", \"recycleRows\", \"\"], [\"table\", \"cdk-table\", \"\", \"recycleRows\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _RecycleViewRepeaterStrategy\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._rowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function DataRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DataRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DataRowOutlet,\n    selectors: [[\"\", \"rowOutlet\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[rowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._headerRowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function HeaderRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HeaderRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: HeaderRowOutlet,\n    selectors: [[\"\", \"headerRowOutlet\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HeaderRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[headerRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._footerRowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function FooterRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FooterRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FooterRowOutlet,\n    selectors: [[\"\", \"footerRowOutlet\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[footerRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._noDataRowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function NoDataRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoDataRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NoDataRowOutlet,\n    selectors: [[\"\", \"noDataRowOutlet\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoDataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[noDataRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n  _differs = inject(IterableDiffers);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n  _viewportRuler = inject(ViewportRuler);\n  _stickyPositioningListener = inject(STICKY_POSITIONING_LISTENER, {\n    optional: true,\n    skipSelf: true\n  });\n  _document = inject(DOCUMENT);\n  /** Latest data provided by the data source. */\n  _data;\n  /** Subject that emits when the component has been destroyed. */\n  _onDestroy = new Subject();\n  /** List of the rendered rows as identified by their `RenderRow` object. */\n  _renderRows;\n  /** Subscription that listens for the data provided by the data source. */\n  _renderChangeSubscription;\n  /**\n   * Map of all the user's defined columns (header, data, and footer cell template) identified by\n   * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n   * any custom column definitions added to `_customColumnDefs`.\n   */\n  _columnDefsByName = new Map();\n  /**\n   * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n   */\n  _rowDefs;\n  /**\n   * Set of all header row definitions that can be used by this table. Populated by the rows\n   * gathered by using `ContentChildren` as well as any custom row definitions added to\n   * `_customHeaderRowDefs`.\n   */\n  _headerRowDefs;\n  /**\n   * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to\n   * `_customFooterRowDefs`.\n   */\n  _footerRowDefs;\n  /** Differ used to find the changes in the data provided by the data source. */\n  _dataDiffer;\n  /** Stores the row definition that does not have a when predicate. */\n  _defaultRowDef;\n  /**\n   * Column definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * column definitions as *its* content child.\n   */\n  _customColumnDefs = new Set();\n  /**\n   * Data row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * built-in data rows as *its* content child.\n   */\n  _customRowDefs = new Set();\n  /**\n   * Header row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * built-in header rows as *its* content child.\n   */\n  _customHeaderRowDefs = new Set();\n  /**\n   * Footer row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n   * built-in footer row as *its* content child.\n   */\n  _customFooterRowDefs = new Set();\n  /** No data row that was defined outside of the direct content children of the table. */\n  _customNoDataRow;\n  /**\n   * Whether the header row definition has been changed. Triggers an update to the header row after\n   * content is checked. Initialized as true so that the table renders the initial set of rows.\n   */\n  _headerRowDefChanged = true;\n  /**\n   * Whether the footer row definition has been changed. Triggers an update to the footer row after\n   * content is checked. Initialized as true so that the table renders the initial set of rows.\n   */\n  _footerRowDefChanged = true;\n  /**\n   * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n   * change.\n   */\n  _stickyColumnStylesNeedReset = true;\n  /**\n   * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n   * `false`, cached values will be used instead. This is only applicable to tables with\n   * `_fixedLayout` enabled. For other tables, cell widths will always be recalculated.\n   */\n  _forceRecalculateCellWidths = true;\n  /**\n   * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n   * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n   * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n   * and row template matches, which allows the `IterableDiffer` to check rows by reference\n   * and understand which rows are added/moved/removed.\n   *\n   * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n   * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n   * contains an array of created pairs. The array is necessary to handle cases where the data\n   * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n   * stored.\n   */\n  _cachedRenderRowsMap = new Map();\n  /** Whether the table is applied to a native `<table>`. */\n  _isNativeHtmlTable;\n  /**\n   * Utility class that is responsible for applying the appropriate sticky positioning styles to\n   * the table's rows and cells.\n   */\n  _stickyStyler;\n  /**\n   * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n   * table subclasses.\n   */\n  stickyCssClass = 'cdk-table-sticky';\n  /**\n   * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n   * the position is set in a selector associated with the value of stickyCssClass. May be\n   * overridden by table subclasses\n   */\n  needsPositionStickyOnElement = true;\n  /** Whether the component is being rendered on the server. */\n  _isServer;\n  /** Whether the no data row is currently showing anything. */\n  _isShowingNoDataRow = false;\n  /** Whether the table has rendered out all the outlets for the first time. */\n  _hasAllOutlets = false;\n  /** Whether the table is done initializing. */\n  _hasInitialized = false;\n  /** Aria role to apply to the table's cells based on the table's own role. */\n  _getCellRole() {\n    // Perform this lazily in case the table's role was updated by a directive after construction.\n    if (this._cellRoleInternal === undefined) {\n      // Note that we set `role=\"cell\"` even on native `td` elements,\n      // because some browsers seem to require it. See #29784.\n      const tableRole = this._elementRef.nativeElement.getAttribute('role');\n      return tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n    }\n    return this._cellRoleInternal;\n  }\n  _cellRoleInternal = undefined;\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  get trackBy() {\n    return this._trackByFn;\n  }\n  set trackBy(fn) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  _trackByFn;\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  _dataSource;\n  /**\n   * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n   * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n   * dataobject will render the first row that evaluates its when predicate to true, in the order\n   * defined in the table, or otherwise the default row which does not have a when predicate.\n   */\n  get multiTemplateDataRows() {\n    return this._multiTemplateDataRows;\n  }\n  set multiTemplateDataRows(value) {\n    this._multiTemplateDataRows = value;\n    // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n    // this setter will be invoked before the row outlet has been defined hence the null check.\n    if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n      this._forceRenderDataRows();\n      this.updateStickyColumnStyles();\n    }\n  }\n  _multiTemplateDataRows = false;\n  /**\n   * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n   * and optimize rendering sticky styles for native tables. No-op for flex tables.\n   */\n  get fixedLayout() {\n    return this._fixedLayout;\n  }\n  set fixedLayout(value) {\n    this._fixedLayout = value;\n    // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n    this._forceRecalculateCellWidths = true;\n    this._stickyColumnStylesNeedReset = true;\n  }\n  _fixedLayout = false;\n  /**\n   * Emits when the table completes rendering a set of data rows based on the latest data from the\n   * data source, even if the set of rows is empty.\n   */\n  contentChanged = new EventEmitter();\n  // TODO(andrewseguin): Remove max value as the end index\n  //   and instead calculate the view on init and scroll.\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   *\n   * @docs-private\n   */\n  viewChange = new BehaviorSubject({\n    start: 0,\n    end: Number.MAX_VALUE\n  });\n  // Outlets in the table's template where the header, data rows, and footer will be inserted.\n  _rowOutlet;\n  _headerRowOutlet;\n  _footerRowOutlet;\n  _noDataRowOutlet;\n  /**\n   * The column definitions provided by the user that contain what the header, data, and footer\n   * cells should render for each column.\n   */\n  _contentColumnDefs;\n  /** Set of data row definitions that were provided to the table as content children. */\n  _contentRowDefs;\n  /** Set of header row definitions that were provided to the table as content children. */\n  _contentHeaderRowDefs;\n  /** Set of footer row definitions that were provided to the table as content children. */\n  _contentFooterRowDefs;\n  /** Row definition that will only be rendered if there's no data in the table. */\n  _noDataRow;\n  _injector = inject(Injector);\n  constructor() {\n    const role = inject(new HostAttributeToken('role'), {\n      optional: true\n    });\n    if (!role) {\n      this._elementRef.nativeElement.setAttribute('role', 'table');\n    }\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n    // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n    // the user has provided a custom trackBy, return the result of that function as evaluated\n    // with the values of the `RenderRow`'s data and index.\n    this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n      return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n    });\n  }\n  ngOnInit() {\n    this._setupStickyStyler();\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this._forceRecalculateCellWidths = true;\n    });\n  }\n  ngAfterContentInit() {\n    this._hasInitialized = true;\n  }\n  ngAfterContentChecked() {\n    // Only start re-rendering in `ngAfterContentChecked` after the first render.\n    if (this._canRender()) {\n      this._render();\n    }\n  }\n  ngOnDestroy() {\n    this._stickyStyler?.destroy();\n    [this._rowOutlet?.viewContainer, this._headerRowOutlet?.viewContainer, this._footerRowOutlet?.viewContainer, this._cachedRenderRowsMap, this._customColumnDefs, this._customRowDefs, this._customHeaderRowDefs, this._customFooterRowDefs, this._columnDefsByName].forEach(def => {\n      def?.clear();\n    });\n    this._headerRowDefs = [];\n    this._footerRowDefs = [];\n    this._defaultRowDef = null;\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n  }\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    this._renderRows = this._getAllRenderRows();\n    const changes = this._dataDiffer.diff(this._renderRows);\n    if (!changes) {\n      this._updateNoDataRow();\n      this.contentChanged.next();\n      return;\n    }\n    const viewContainer = this._rowOutlet.viewContainer;\n    this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, change => {\n      if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n        this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n      }\n    });\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange(record => {\n      const rowView = viewContainer.get(record.currentIndex);\n      rowView.context.$implicit = record.item.data;\n    });\n    this._updateNoDataRow();\n    this.contentChanged.next();\n    this.updateStickyColumnStyles();\n  }\n  /** Adds a column definition that was not included as part of the content children. */\n  addColumnDef(columnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n  /** Removes a column definition that was not included as part of the content children. */\n  removeColumnDef(columnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n  /** Adds a row definition that was not included as part of the content children. */\n  addRowDef(rowDef) {\n    this._customRowDefs.add(rowDef);\n  }\n  /** Removes a row definition that was not included as part of the content children. */\n  removeRowDef(rowDef) {\n    this._customRowDefs.delete(rowDef);\n  }\n  /** Adds a header row definition that was not included as part of the content children. */\n  addHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.add(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Removes a header row definition that was not included as part of the content children. */\n  removeHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.delete(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Adds a footer row definition that was not included as part of the content children. */\n  addFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.add(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Removes a footer row definition that was not included as part of the content children. */\n  removeFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.delete(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Sets a no data row definition that was not included as a part of the content children. */\n  setNoDataRow(noDataRow) {\n    this._customNoDataRow = noDataRow;\n  }\n  /**\n   * Updates the header sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n   * automatically called when the header row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyHeaderRowStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    // Hide the thead element if there are no header rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const thead = closestTableSection(this._headerRowOutlet, 'thead');\n      if (thead) {\n        thead.style.display = headerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._headerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n    this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._headerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n   * automatically called when the footer row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyFooterRowStyles() {\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n      if (tfoot) {\n        tfoot.style.display = footerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._footerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n    this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n    this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._footerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the column sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the left and right. Then sticky styles are added for the left and right according\n   * to the column definitions for each cell in each row. This is automatically called when\n   * the data source provides a new set of data or when a column definition changes its sticky\n   * input. May be called manually for cases where the cell content changes outside of these events.\n   */\n  updateStickyColumnStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const dataRows = this._getRenderedRows(this._rowOutlet);\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n    // In a table using a fixed layout, row content won't affect column width, so sticky styles\n    // don't need to be cleared unless either the sticky column config changes or one of the row\n    // defs change.\n    if (this._isNativeHtmlTable && !this._fixedLayout || this._stickyColumnStylesNeedReset) {\n      // Clear the left and right positioning from all columns in the table across all rows since\n      // sticky columns span across all table sections (header, data, footer)\n      this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n      this._stickyColumnStylesNeedReset = false;\n    }\n    // Update the sticky styles for each header row depending on the def's sticky state\n    headerRows.forEach((headerRow, i) => {\n      this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n    });\n    // Update the sticky styles for each data row depending on its def's sticky state\n    this._rowDefs.forEach(rowDef => {\n      // Collect all the rows rendered with this row definition.\n      const rows = [];\n      for (let i = 0; i < dataRows.length; i++) {\n        if (this._renderRows[i].rowDef === rowDef) {\n          rows.push(dataRows[i]);\n        }\n      }\n      this._addStickyColumnStyles(rows, rowDef);\n    });\n    // Update the sticky styles for each footer row depending on the def's sticky state\n    footerRows.forEach((footerRow, i) => {\n      this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n    });\n    // Reset the dirty state of the sticky input change since it has been used.\n    Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n  }\n  /** Invoked whenever an outlet is created and has been assigned to the table. */\n  _outletAssigned() {\n    // Trigger the first render once all outlets have been assigned. We do it this way, as\n    // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n    // the next change detection will happen.\n    // Also we can't use queries to resolve the outlets, because they're wrapped in a\n    // conditional, so we have to rely on them being assigned via DI.\n    if (!this._hasAllOutlets && this._rowOutlet && this._headerRowOutlet && this._footerRowOutlet && this._noDataRowOutlet) {\n      this._hasAllOutlets = true;\n      // In some setups this may fire before `ngAfterContentInit`\n      // so we need a check here. See #28538.\n      if (this._canRender()) {\n        this._render();\n      }\n    }\n  }\n  /** Whether the table has all the information to start rendering. */\n  _canRender() {\n    return this._hasAllOutlets && this._hasInitialized;\n  }\n  /** Renders the table if its state has changed. */\n  _render() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n    // Make sure that the user has at least added header, footer, or data row def.\n    if (!this._headerRowDefs.length && !this._footerRowDefs.length && !this._rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingRowDefsError();\n    }\n    // Render updates if the list of columns have been changed for the header, row, or footer defs.\n    const columnsChanged = this._renderUpdatedColumns();\n    const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n    // Ensure sticky column styles are reset if set to `true` elsewhere.\n    this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n    this._forceRecalculateCellWidths = rowDefsChanged;\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._forceRenderHeaderRows();\n      this._headerRowDefChanged = false;\n    }\n    // If the footer row definition has been changed, trigger a render to the footer row.\n    if (this._footerRowDefChanged) {\n      this._forceRenderFooterRows();\n      this._footerRowDefChanged = false;\n    }\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    } else if (this._stickyColumnStylesNeedReset) {\n      // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n      // called when it row data arrives. Otherwise, we need to call it proactively.\n      this.updateStickyColumnStyles();\n    }\n    this._checkStickyStates();\n  }\n  /**\n   * Get the list of RenderRow objects to render according to the current list of data and defined\n   * row definitions. If the previous list already contained a particular pair, it should be reused\n   * so that the differ equates their references.\n   */\n  _getAllRenderRows() {\n    const renderRows = [];\n    // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n    // new cache while unused ones can be picked up by garbage collection.\n    const prevCachedRenderRows = this._cachedRenderRowsMap;\n    this._cachedRenderRowsMap = new Map();\n    if (!this._data) {\n      return renderRows;\n    }\n    // For each data object, get the list of rows that should be rendered, represented by the\n    // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n    for (let i = 0; i < this._data.length; i++) {\n      let data = this._data[i];\n      const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n      if (!this._cachedRenderRowsMap.has(data)) {\n        this._cachedRenderRowsMap.set(data, new WeakMap());\n      }\n      for (let j = 0; j < renderRowsForData.length; j++) {\n        let renderRow = renderRowsForData[j];\n        const cache = this._cachedRenderRowsMap.get(renderRow.data);\n        if (cache.has(renderRow.rowDef)) {\n          cache.get(renderRow.rowDef).push(renderRow);\n        } else {\n          cache.set(renderRow.rowDef, [renderRow]);\n        }\n        renderRows.push(renderRow);\n      }\n    }\n    return renderRows;\n  }\n  /**\n   * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n   * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n   * `(T, CdkRowDef)` pair.\n   */\n  _getRenderRowsForData(data, dataIndex, cache) {\n    const rowDefs = this._getRowDefs(data, dataIndex);\n    return rowDefs.map(rowDef => {\n      const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n      if (cachedRenderRows.length) {\n        const dataRow = cachedRenderRows.shift();\n        dataRow.dataIndex = dataIndex;\n        return dataRow;\n      } else {\n        return {\n          data,\n          rowDef,\n          dataIndex\n        };\n      }\n    });\n  }\n  /** Update the map containing the content's column definitions. */\n  _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n    const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n  /** Update the list of all available row definitions that can be used. */\n  _cacheRowDefs() {\n    this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n    this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n    this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n    // After all row definitions are determined, find the row definition to be considered default.\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (!this.multiTemplateDataRows && defaultRowDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMultipleDefaultRowDefsError();\n    }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n  /**\n   * Check if the header, data, or footer rows have changed what columns they want to display or\n   * whether the sticky states have changed for the header or footer. If there is a diff, then\n   * re-render that section.\n   */\n  _renderUpdatedColumns() {\n    const columnsDiffReducer = (acc, def) => {\n      // The differ should be run for every column, even if `acc` is already\n      // true (see #29922)\n      const diff = !!def.getColumnsDiff();\n      return acc || diff;\n    };\n    // Force re-render data rows if the list of column definitions have changed.\n    const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n    if (dataColumnsChanged) {\n      this._forceRenderDataRows();\n    }\n    // Force re-render header/footer rows if the list of column definitions have changed.\n    const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n    if (headerColumnsChanged) {\n      this._forceRenderHeaderRows();\n    }\n    const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n    if (footerColumnsChanged) {\n      this._forceRenderFooterRows();\n    }\n    return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    this._data = [];\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      if (this._rowOutlet) {\n        this._rowOutlet.viewContainer.clear();\n      }\n    }\n    this._dataSource = dataSource;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this.dataSource)) {\n      dataStream = this.dataSource.connect(this);\n    } else if (isObservable(this.dataSource)) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = of(this.dataSource);\n    }\n    if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableUnknownDataSourceError();\n    }\n    this._renderChangeSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this._data = data || [];\n      this.renderRows();\n    });\n  }\n  /**\n   * Clears any existing content in the header row outlet and creates a new embedded view\n   * in the outlet using the header row definition.\n   */\n  _forceRenderHeaderRows() {\n    // Clear the header row outlet if any content exists.\n    if (this._headerRowOutlet.viewContainer.length > 0) {\n      this._headerRowOutlet.viewContainer.clear();\n    }\n    this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n    this.updateStickyHeaderRowStyles();\n  }\n  /**\n   * Clears any existing content in the footer row outlet and creates a new embedded view\n   * in the outlet using the footer row definition.\n   */\n  _forceRenderFooterRows() {\n    // Clear the footer row outlet if any content exists.\n    if (this._footerRowOutlet.viewContainer.length > 0) {\n      this._footerRowOutlet.viewContainer.clear();\n    }\n    this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n    this.updateStickyFooterRowStyles();\n  }\n  /** Adds the sticky column styles for the rows according to the columns' stick states. */\n  _addStickyColumnStyles(rows, rowDef) {\n    const columnDefs = Array.from(rowDef?.columns || []).map(columnName => {\n      const columnDef = this._columnDefsByName.get(columnName);\n      if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnName);\n      }\n      return columnDef;\n    });\n    const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n    const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n    this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n  }\n  /** Gets the list of rows that have been rendered in the row outlet. */\n  _getRenderedRows(rowOutlet) {\n    const renderedRows = [];\n    for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n      const viewRef = rowOutlet.viewContainer.get(i);\n      renderedRows.push(viewRef.rootNodes[0]);\n    }\n    return renderedRows;\n  }\n  /**\n   * Get the matching row definitions that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definitions that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDefs(data, dataIndex) {\n    if (this._rowDefs.length == 1) {\n      return [this._rowDefs[0]];\n    }\n    let rowDefs = [];\n    if (this.multiTemplateDataRows) {\n      rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n    } else {\n      let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n      if (rowDef) {\n        rowDefs.push(rowDef);\n      }\n    }\n    if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingMatchingRowDefError(data);\n    }\n    return rowDefs;\n  }\n  _getEmbeddedViewArgs(renderRow, index) {\n    const rowDef = renderRow.rowDef;\n    const context = {\n      $implicit: renderRow.data\n    };\n    return {\n      templateRef: rowDef.template,\n      context,\n      index\n    };\n  }\n  /**\n   * Creates a new row template in the outlet and fills it with the set of cell templates.\n   * Optionally takes a context to provide to the row and cells, as well as an optional index\n   * of where to place the new row template in the outlet.\n   */\n  _renderRow(outlet, rowDef, index, context = {}) {\n    // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n    const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n    this._renderCellTemplateForItem(rowDef, context);\n    return view;\n  }\n  _renderCellTemplateForItem(rowDef, context) {\n    for (let cellTemplate of this._getCellTemplates(rowDef)) {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  _updateRowIndexContext() {\n    const viewContainer = this._rowOutlet.viewContainer;\n    for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n      const viewRef = viewContainer.get(renderIndex);\n      const context = viewRef.context;\n      context.count = count;\n      context.first = renderIndex === 0;\n      context.last = renderIndex === count - 1;\n      context.even = renderIndex % 2 === 0;\n      context.odd = !context.even;\n      if (this.multiTemplateDataRows) {\n        context.dataIndex = this._renderRows[renderIndex].dataIndex;\n        context.renderIndex = renderIndex;\n      } else {\n        context.index = this._renderRows[renderIndex].dataIndex;\n      }\n    }\n  }\n  /** Gets the column definitions for the provided row def. */\n  _getCellTemplates(rowDef) {\n    if (!rowDef || !rowDef.columns) {\n      return [];\n    }\n    return Array.from(rowDef.columns, columnId => {\n      const column = this._columnDefsByName.get(columnId);\n      if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnId);\n      }\n      return rowDef.extractCellTemplate(column);\n    });\n  }\n  /**\n   * Forces a re-render of the data rows. Should be called in cases where there has been an input\n   * change that affects the evaluation of which rows should be rendered, e.g. toggling\n   * `multiTemplateDataRows` or adding/removing row definitions.\n   */\n  _forceRenderDataRows() {\n    this._dataDiffer.diff([]);\n    this._rowOutlet.viewContainer.clear();\n    this.renderRows();\n  }\n  /**\n   * Checks if there has been a change in sticky states since last check and applies the correct\n   * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n   * during a change detection and after the inputs are settled (after content check).\n   */\n  _checkStickyStates() {\n    const stickyCheckReducer = (acc, d) => {\n      return acc || d.hasStickyChanged();\n    };\n    // Note that the check needs to occur for every definition since it notifies the definition\n    // that it can reset its dirty state. Using another operator like `some` may short-circuit\n    // remaining definitions and leave them in an unchecked state.\n    if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyHeaderRowStyles();\n    }\n    if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyFooterRowStyles();\n    }\n    if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n      this._stickyColumnStylesNeedReset = true;\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Creates the sticky styler that will be used for sticky rows and columns. Listens\n   * for directionality changes and provides the latest direction to the styler. Re-applies column\n   * stickiness when directionality changes.\n   */\n  _setupStickyStyler() {\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, this._platform.isBrowser, this.needsPositionStickyOnElement, direction, this._stickyPositioningListener, this._injector);\n    (this._dir ? this._dir.change : of()).pipe(takeUntil(this._onDestroy)).subscribe(value => {\n      this._stickyStyler.direction = value;\n      this.updateStickyColumnStyles();\n    });\n  }\n  /** Filters definitions that belong to this table from a QueryList. */\n  _getOwnDefs(items) {\n    return items.filter(item => !item._table || item._table === this);\n  }\n  /** Creates or removes the no data row, depending on whether any data is being shown. */\n  _updateNoDataRow() {\n    const noDataRow = this._customNoDataRow || this._noDataRow;\n    if (!noDataRow) {\n      return;\n    }\n    const shouldShow = this._rowOutlet.viewContainer.length === 0;\n    if (shouldShow === this._isShowingNoDataRow) {\n      return;\n    }\n    const container = this._noDataRowOutlet.viewContainer;\n    if (shouldShow) {\n      const view = container.createEmbeddedView(noDataRow.templateRef);\n      const rootNode = view.rootNodes[0];\n      // Only add the attributes if we have a single root node since it's hard\n      // to figure out which one to add it to when there are multiple.\n      if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n        rootNode.setAttribute('role', 'row');\n        rootNode.classList.add(noDataRow._contentClassName);\n      }\n    } else {\n      container.clear();\n    }\n    this._isShowingNoDataRow = shouldShow;\n    this._changeDetectorRef.markForCheck();\n  }\n  static ɵfac = function CdkTable_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTable)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkTable,\n    selectors: [[\"cdk-table\"], [\"table\", \"cdk-table\", \"\"]],\n    contentQueries: function CdkTable_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CdkNoDataRow, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkColumnDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkRowDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkHeaderRowDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkFooterRowDef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRow = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentColumnDefs = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentRowDefs = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentHeaderRowDefs = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentFooterRowDefs = _t);\n      }\n    },\n    hostAttrs: [1, \"cdk-table\"],\n    hostVars: 2,\n    hostBindings: function CdkTable_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cdk-table-fixed-layout\", ctx.fixedLayout);\n      }\n    },\n    inputs: {\n      trackBy: \"trackBy\",\n      dataSource: \"dataSource\",\n      multiTemplateDataRows: [2, \"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute],\n      fixedLayout: [2, \"fixedLayout\", \"fixedLayout\", booleanAttribute]\n    },\n    outputs: {\n      contentChanged: \"contentChanged\"\n    },\n    exportAs: [\"cdkTable\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_TABLE,\n      useExisting: CdkTable\n    }, {\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _DisposeViewRepeaterStrategy\n    },\n    // Prevent nested tables from seeing this table's StickyPositioningListener.\n    {\n      provide: STICKY_POSITIONING_LISTENER,\n      useValue: null\n    }])],\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 2,\n    consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n    template: function CdkTable_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵprojection(0);\n        i0.ɵɵprojection(1, 1);\n        i0.ɵɵconditionalCreate(2, CdkTable_Conditional_2_Template, 1, 0);\n        i0.ɵɵconditionalCreate(3, CdkTable_Conditional_3_Template, 7, 0)(4, CdkTable_Conditional_4_Template, 4, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isServer ? 2 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._isNativeHtmlTable ? 3 : 4);\n      }\n    },\n    dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n    styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTable, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-table, table[cdk-table]',\n      exportAs: 'cdkTable',\n      template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `,\n      host: {\n        'class': 'cdk-table',\n        '[class.cdk-table-fixed-layout]': 'fixedLayout'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"]\n    }]\n  }], () => [], {\n    trackBy: [{\n      type: Input\n    }],\n    dataSource: [{\n      type: Input\n    }],\n    multiTemplateDataRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fixedLayout: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    contentChanged: [{\n      type: Output\n    }],\n    _contentColumnDefs: [{\n      type: ContentChildren,\n      args: [CdkColumnDef, {\n        descendants: true\n      }]\n    }],\n    _contentRowDefs: [{\n      type: ContentChildren,\n      args: [CdkRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentHeaderRowDefs: [{\n      type: ContentChildren,\n      args: [CdkHeaderRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentFooterRowDefs: [{\n      type: ContentChildren,\n      args: [CdkFooterRowDef, {\n        descendants: true\n      }]\n    }],\n    _noDataRow: [{\n      type: ContentChild,\n      args: [CdkNoDataRow]\n    }]\n  });\n})();\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n  return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n  const uppercaseSection = section.toUpperCase();\n  let current = outlet.viewContainer.element.nativeElement;\n  while (current) {\n    // 1 is an element node.\n    const nodeName = current.nodeType === 1 ? current.nodeName : null;\n    if (nodeName === uppercaseSection) {\n      return current;\n    } else if (nodeName === 'TABLE') {\n      // Stop traversing past the `table` node.\n      break;\n    }\n    current = current.parentNode;\n  }\n  return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n  _table = inject(CdkTable, {\n    optional: true\n  });\n  _options = inject(TEXT_COLUMN_OPTIONS, {\n    optional: true\n  });\n  /** Column name that should be used to reference this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._name = name;\n    // With Ivy, inputs can be initialized before static query results are\n    // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n    this._syncColumnDefName();\n  }\n  _name;\n  /**\n   * Text label that should be used for the column header. If this property is not\n   * set, the header text will default to the column name with its first letter capitalized.\n   */\n  headerText;\n  /**\n   * Accessor function to retrieve the data rendered for each cell. If this\n   * property is not set, the data cells will render the value found in the data's property matching\n   * the column's name. For example, if the column is named `id`, then the rendered value will be\n   * value defined by the data's `id` property.\n   */\n  dataAccessor;\n  /** Alignment of the cell values. */\n  justify = 'start';\n  /** @docs-private */\n  columnDef;\n  /**\n   * The column cell is provided to the column during `ngOnInit` with a static query.\n   * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n   * column definition was provided in the same view as the table, which is not the case with this\n   * component.\n   * @docs-private\n   */\n  cell;\n  /**\n   * The column headerCell is provided to the column during `ngOnInit` with a static query.\n   * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n   * column definition was provided in the same view as the table, which is not the case with this\n   * component.\n   * @docs-private\n   */\n  headerCell;\n  constructor() {\n    this._options = this._options || {};\n  }\n  ngOnInit() {\n    this._syncColumnDefName();\n    if (this.headerText === undefined) {\n      this.headerText = this._createDefaultHeaderText();\n    }\n    if (!this.dataAccessor) {\n      this.dataAccessor = this._options.defaultDataAccessor || ((data, name) => data[name]);\n    }\n    if (this._table) {\n      // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n      // since the columnDef will not pick up its content by the time the table finishes checking\n      // its content and initializing the rows.\n      this.columnDef.cell = this.cell;\n      this.columnDef.headerCell = this.headerCell;\n      this._table.addColumnDef(this.columnDef);\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTableTextColumnMissingParentTableError();\n    }\n  }\n  ngOnDestroy() {\n    if (this._table) {\n      this._table.removeColumnDef(this.columnDef);\n    }\n  }\n  /**\n   * Creates a default header text. Use the options' header text transformation function if one\n   * has been provided. Otherwise simply capitalize the column name.\n   */\n  _createDefaultHeaderText() {\n    const name = this.name;\n    if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableTextColumnMissingNameError();\n    }\n    if (this._options && this._options.defaultHeaderTextTransform) {\n      return this._options.defaultHeaderTextTransform(name);\n    }\n    return name[0].toUpperCase() + name.slice(1);\n  }\n  /** Synchronizes the column definition name with the text column name. */\n  _syncColumnDefName() {\n    if (this.columnDef) {\n      this.columnDef.name = this.name;\n    }\n  }\n  static ɵfac = function CdkTextColumn_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTextColumn)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkTextColumn,\n    selectors: [[\"cdk-text-column\"]],\n    viewQuery: function CdkTextColumn_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkColumnDef, 7);\n        i0.ɵɵviewQuery(CdkCellDef, 7);\n        i0.ɵɵviewQuery(CdkHeaderCellDef, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnDef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n      }\n    },\n    inputs: {\n      name: \"name\",\n      headerText: \"headerText\",\n      dataAccessor: \"dataAccessor\",\n      justify: \"justify\"\n    },\n    decls: 3,\n    vars: 0,\n    consts: [[\"cdkColumnDef\", \"\"], [\"cdk-header-cell\", \"\", 3, \"text-align\", 4, \"cdkHeaderCellDef\"], [\"cdk-cell\", \"\", 3, \"text-align\", 4, \"cdkCellDef\"], [\"cdk-header-cell\", \"\"], [\"cdk-cell\", \"\"]],\n    template: function CdkTextColumn_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0, 0);\n        i0.ɵɵtemplate(1, CdkTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, CdkTextColumn_td_2_Template, 2, 3, \"td\", 2);\n        i0.ɵɵelementContainerEnd();\n      }\n    },\n    dependencies: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-text-column',\n      template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell]\n    }]\n  }], () => [], {\n    name: [{\n      type: Input\n    }],\n    headerText: [{\n      type: Input\n    }],\n    dataAccessor: [{\n      type: Input\n    }],\n    justify: [{\n      type: Input\n    }],\n    columnDef: [{\n      type: ViewChild,\n      args: [CdkColumnDef, {\n        static: true\n      }]\n    }],\n    cell: [{\n      type: ViewChild,\n      args: [CdkCellDef, {\n        static: true\n      }]\n    }],\n    headerCell: [{\n      type: ViewChild,\n      args: [CdkHeaderCellDef, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet];\nclass CdkTableModule {\n  static ɵfac = function CdkTableModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTableModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkTableModule,\n    imports: [ScrollingModule, CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet],\n    exports: [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ScrollingModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTableModule, [{\n    type: NgModule,\n    args: [{\n      exports: EXPORTED_DECLARATIONS,\n      imports: [ScrollingModule, ...EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_POSITIONING_LISTENER, TEXT_COLUMN_OPTIONS };\n", "import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, booleanAttribute, NgModule } from '@angular/core';\nimport { CdkTable, CDK_TABLE, STICKY_POSITIONING_LISTENER, HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkCellOutlet, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction MatTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 2);\n    i0.ɵɵelementContainer(3, 3)(4, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 3)(2, 4)(3, 5);\n  }\n}\nfunction MatTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction MatTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nclass MatRecycleRows {\n  static ɵfac = function MatRecycleRows_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRecycleRows)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatRecycleRows,\n    selectors: [[\"mat-table\", \"recycleRows\", \"\"], [\"table\", \"mat-table\", \"\", \"recycleRows\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _RecycleViewRepeaterStrategy\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\nclass MatTable extends CdkTable {\n  /** Overrides the sticky CSS class set by the `CdkTable`. */\n  stickyCssClass = 'mat-mdc-table-sticky';\n  /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n  needsPositionStickyOnElement = false;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTable_BaseFactory;\n    return function MatTable_Factory(__ngFactoryType__) {\n      return (ɵMatTable_BaseFactory || (ɵMatTable_BaseFactory = i0.ɵɵgetInheritedFactory(MatTable)))(__ngFactoryType__ || MatTable);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTable,\n    selectors: [[\"mat-table\"], [\"table\", \"mat-table\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-table\", \"mdc-data-table__table\"],\n    hostVars: 2,\n    hostBindings: function MatTable_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-table-fixed-layout\", ctx.fixedLayout);\n      }\n    },\n    exportAs: [\"matTable\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTable,\n      useExisting: MatTable\n    }, {\n      provide: CDK_TABLE,\n      useExisting: MatTable\n    },\n    // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n    //  is only included in the build if used.\n    {\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _DisposeViewRepeaterStrategy\n    },\n    // Prevent nested tables from seeing this table's StickyPositioningListener.\n    {\n      provide: STICKY_POSITIONING_LISTENER,\n      useValue: null\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 2,\n    consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"role\", \"rowgroup\", 1, \"mdc-data-table__content\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n    template: function MatTable_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵprojection(0);\n        i0.ɵɵprojection(1, 1);\n        i0.ɵɵconditionalCreate(2, MatTable_Conditional_2_Template, 1, 0);\n        i0.ɵɵconditionalCreate(3, MatTable_Conditional_3_Template, 7, 0)(4, MatTable_Conditional_4_Template, 4, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isServer ? 2 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._isNativeHtmlTable ? 3 : 4);\n      }\n    },\n    dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n    styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTable, [{\n    type: Component,\n    args: [{\n      selector: 'mat-table, table[mat-table]',\n      exportAs: 'matTable',\n      template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `,\n      host: {\n        'class': 'mat-mdc-table mdc-data-table__table',\n        '[class.mdc-table-fixed-layout]': 'fixedLayout'\n      },\n      providers: [{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatCellDef_BaseFactory;\n    return function MatCellDef_Factory(__ngFactoryType__) {\n      return (ɵMatCellDef_BaseFactory || (ɵMatCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatCellDef)))(__ngFactoryType__ || MatCellDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCellDef,\n    selectors: [[\"\", \"matCellDef\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkCellDef,\n      useExisting: MatCellDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matCellDef]',\n      providers: [{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatHeaderCellDef_BaseFactory;\n    return function MatHeaderCellDef_Factory(__ngFactoryType__) {\n      return (ɵMatHeaderCellDef_BaseFactory || (ɵMatHeaderCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCellDef)))(__ngFactoryType__ || MatHeaderCellDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatHeaderCellDef,\n    selectors: [[\"\", \"matHeaderCellDef\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkHeaderCellDef,\n      useExisting: MatHeaderCellDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderCellDef]',\n      providers: [{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatFooterCellDef_BaseFactory;\n    return function MatFooterCellDef_Factory(__ngFactoryType__) {\n      return (ɵMatFooterCellDef_BaseFactory || (ɵMatFooterCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCellDef)))(__ngFactoryType__ || MatFooterCellDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatFooterCellDef,\n    selectors: [[\"\", \"matFooterCellDef\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkFooterCellDef,\n      useExisting: MatFooterCellDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterCellDef]',\n      providers: [{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /**\n   * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n   * In the future, this will only add \"mat-column-\" and columnCssClassName\n   * will change from type string[] to string.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    super._updateColumnCssClassName();\n    this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatColumnDef_BaseFactory;\n    return function MatColumnDef_Factory(__ngFactoryType__) {\n      return (ɵMatColumnDef_BaseFactory || (ɵMatColumnDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatColumnDef)))(__ngFactoryType__ || MatColumnDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatColumnDef,\n    selectors: [[\"\", \"matColumnDef\", \"\"]],\n    inputs: {\n      name: [0, \"matColumnDef\", \"name\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkColumnDef,\n      useExisting: MatColumnDef\n    }, {\n      provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n      useExisting: MatColumnDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matColumnDef]',\n      providers: [{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]\n    }]\n  }], null, {\n    name: [{\n      type: Input,\n      args: ['matColumnDef']\n    }]\n  });\n})();\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatHeaderCell_BaseFactory;\n    return function MatHeaderCell_Factory(__ngFactoryType__) {\n      return (ɵMatHeaderCell_BaseFactory || (ɵMatHeaderCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCell)))(__ngFactoryType__ || MatHeaderCell);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatHeaderCell,\n    selectors: [[\"mat-header-cell\"], [\"th\", \"mat-header-cell\", \"\"]],\n    hostAttrs: [\"role\", \"columnheader\", 1, \"mat-mdc-header-cell\", \"mdc-data-table__header-cell\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-header-cell, th[mat-header-cell]',\n      host: {\n        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], null, null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatFooterCell_BaseFactory;\n    return function MatFooterCell_Factory(__ngFactoryType__) {\n      return (ɵMatFooterCell_BaseFactory || (ɵMatFooterCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCell)))(__ngFactoryType__ || MatFooterCell);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatFooterCell,\n    selectors: [[\"mat-footer-cell\"], [\"td\", \"mat-footer-cell\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-footer-cell\", \"mdc-data-table__cell\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-footer-cell, td[mat-footer-cell]',\n      host: {\n        'class': 'mat-mdc-footer-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatCell_BaseFactory;\n    return function MatCell_Factory(__ngFactoryType__) {\n      return (ɵMatCell_BaseFactory || (ɵMatCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatCell)))(__ngFactoryType__ || MatCell);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCell,\n    selectors: [[\"mat-cell\"], [\"td\", \"mat-cell\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-cell\", \"mdc-data-table__cell\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-cell, td[mat-cell]',\n      host: {\n        'class': 'mat-mdc-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatHeaderRowDef_BaseFactory;\n    return function MatHeaderRowDef_Factory(__ngFactoryType__) {\n      return (ɵMatHeaderRowDef_BaseFactory || (ɵMatHeaderRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRowDef)))(__ngFactoryType__ || MatHeaderRowDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatHeaderRowDef,\n    selectors: [[\"\", \"matHeaderRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"matHeaderRowDef\", \"columns\"],\n      sticky: [2, \"matHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkHeaderRowDef,\n      useExisting: MatHeaderRowDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderRowDef]',\n      providers: [{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matHeaderRowDef'\n      }, {\n        name: 'sticky',\n        alias: 'matHeaderRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatFooterRowDef_BaseFactory;\n    return function MatFooterRowDef_Factory(__ngFactoryType__) {\n      return (ɵMatFooterRowDef_BaseFactory || (ɵMatFooterRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRowDef)))(__ngFactoryType__ || MatFooterRowDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatFooterRowDef,\n    selectors: [[\"\", \"matFooterRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"matFooterRowDef\", \"columns\"],\n      sticky: [2, \"matFooterRowDefSticky\", \"sticky\", booleanAttribute]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkFooterRowDef,\n      useExisting: MatFooterRowDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterRowDef]',\n      providers: [{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matFooterRowDef'\n      }, {\n        name: 'sticky',\n        alias: 'matFooterRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatRowDef_BaseFactory;\n    return function MatRowDef_Factory(__ngFactoryType__) {\n      return (ɵMatRowDef_BaseFactory || (ɵMatRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatRowDef)))(__ngFactoryType__ || MatRowDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatRowDef,\n    selectors: [[\"\", \"matRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"matRowDefColumns\", \"columns\"],\n      when: [0, \"matRowDefWhen\", \"when\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkRowDef,\n      useExisting: MatRowDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matRowDef]',\n      providers: [{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'matRowDefWhen'\n      }]\n    }]\n  }], null, null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatHeaderRow_BaseFactory;\n    return function MatHeaderRow_Factory(__ngFactoryType__) {\n      return (ɵMatHeaderRow_BaseFactory || (ɵMatHeaderRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRow)))(__ngFactoryType__ || MatHeaderRow);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatHeaderRow,\n    selectors: [[\"mat-header-row\"], [\"tr\", \"mat-header-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-header-row\", \"mdc-data-table__header-row\"],\n    exportAs: [\"matHeaderRow\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkHeaderRow,\n      useExisting: MatHeaderRow\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function MatHeaderRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-header-row, tr[mat-header-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matHeaderRow',\n      providers: [{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }],\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatFooterRow_BaseFactory;\n    return function MatFooterRow_Factory(__ngFactoryType__) {\n      return (ɵMatFooterRow_BaseFactory || (ɵMatFooterRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRow)))(__ngFactoryType__ || MatFooterRow);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatFooterRow,\n    selectors: [[\"mat-footer-row\"], [\"tr\", \"mat-footer-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-footer-row\", \"mdc-data-table__row\"],\n    exportAs: [\"matFooterRow\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkFooterRow,\n      useExisting: MatFooterRow\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function MatFooterRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-footer-row, tr[mat-footer-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-footer-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matFooterRow',\n      providers: [{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }],\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatRow_BaseFactory;\n    return function MatRow_Factory(__ngFactoryType__) {\n      return (ɵMatRow_BaseFactory || (ɵMatRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatRow)))(__ngFactoryType__ || MatRow);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatRow,\n    selectors: [[\"mat-row\"], [\"tr\", \"mat-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-row\", \"mdc-data-table__row\"],\n    exportAs: [\"matRow\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkRow,\n      useExisting: MatRow\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function MatRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-row, tr[mat-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matRow',\n      providers: [{\n        provide: CdkRow,\n        useExisting: MatRow\n      }],\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n  _contentClassName = 'mat-mdc-no-data-row';\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatNoDataRow_BaseFactory;\n    return function MatNoDataRow_Factory(__ngFactoryType__) {\n      return (ɵMatNoDataRow_BaseFactory || (ɵMatNoDataRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatNoDataRow)))(__ngFactoryType__ || MatNoDataRow);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatNoDataRow,\n    selectors: [[\"ng-template\", \"matNoDataRow\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkNoDataRow,\n      useExisting: MatNoDataRow\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matNoDataRow]',\n      providers: [{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTextColumn_BaseFactory;\n    return function MatTextColumn_Factory(__ngFactoryType__) {\n      return (ɵMatTextColumn_BaseFactory || (ɵMatTextColumn_BaseFactory = i0.ɵɵgetInheritedFactory(MatTextColumn)))(__ngFactoryType__ || MatTextColumn);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTextColumn,\n    selectors: [[\"mat-text-column\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[\"matColumnDef\", \"\"], [\"mat-header-cell\", \"\", 3, \"text-align\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 3, \"text-align\", 4, \"matCellDef\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"]],\n    template: function MatTextColumn_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0, 0);\n        i0.ɵɵtemplate(1, MatTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, MatTextColumn_td_2_Template, 2, 3, \"td\", 2);\n        i0.ɵɵelementContainerEnd();\n      }\n    },\n    dependencies: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'mat-text-column',\n      template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell]\n    }]\n  }], null, null);\n})();\nconst EXPORTED_DECLARATIONS = [\n// Table\nMatTable, MatRecycleRows,\n// Template defs\nMatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n// Cell directives\nMatHeaderCell, MatCell, MatFooterCell,\n// Row directives\nMatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn];\nclass MatTableModule {\n  static ɵfac = function MatTableModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTableModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTableModule,\n    imports: [MatCommonModule, CdkTableModule,\n    // Table\n    MatTable, MatRecycleRows,\n    // Template defs\n    MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n    // Cell directives\n    MatHeaderCell, MatCell, MatFooterCell,\n    // Row directives\n    MatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn],\n    exports: [MatCommonModule,\n    // Table\n    MatTable, MatRecycleRows,\n    // Template defs\n    MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n    // Cell directives\n    MatHeaderCell, MatCell, MatFooterCell,\n    // Row directives\n    MatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, CdkTableModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkTableModule, ...EXPORTED_DECLARATIONS],\n      exports: [MatCommonModule, EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends DataSource {\n  /** Stream that emits when a new data array is set on the data source. */\n  _data;\n  /** Stream emitting render data to the table (depends on ordered data changes). */\n  _renderData = new BehaviorSubject([]);\n  /** Stream that emits when a new filter string is set on the data source. */\n  _filter = new BehaviorSubject('');\n  /** Used to react to internal changes of the paginator that are made by the data source itself. */\n  _internalPageChanges = new Subject();\n  /**\n   * Subscription to the changes that should trigger an update to the table's rendered rows, such\n   * as filtering, sorting, pagination, or base data changes.\n   */\n  _renderChangesSubscription = null;\n  /**\n   * The filtered set of data that has been matched by the filter string, or all the data if there\n   * is no filter. Useful for knowing the set of data the table represents.\n   * For example, a 'selectAll()' function would likely want to select the set of filtered data\n   * shown to the user rather than all the data.\n   */\n  filteredData;\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter() {\n    return this._filter.value;\n  }\n  set filter(filter) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort() {\n    return this._sort;\n  }\n  set sort(sort) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n  _sort;\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator() {\n    return this._paginator;\n  }\n  set paginator(paginator) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n  _paginator;\n  /**\n   * Data accessor function that is used for accessing data properties for sorting through\n   * the default sortData function.\n   * This default function assumes that the sort header IDs (which defaults to the column name)\n   * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n   * May be set to a custom function for different behavior.\n   * @param data Data object that is being accessed.\n   * @param sortHeaderId The name of the column that represents the data.\n   */\n  sortingDataAccessor = (data, sortHeaderId) => {\n    const value = data[sortHeaderId];\n    if (_isNumberValue(value)) {\n      const numberValue = Number(value);\n      // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we leave them as strings.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER\n      return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n    }\n    return value;\n  };\n  /**\n   * Gets a sorted copy of the data array based on the state of the MatSort. Called\n   * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n   * By default, the function retrieves the active sort and its direction and compares data\n   * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n   * of data ordering.\n   * @param data The array of data that should be sorted.\n   * @param sort The connected MatSort that holds the current sort state.\n   */\n  sortData = (data, sort) => {\n    const active = sort.active;\n    const direction = sort.direction;\n    if (!active || direction == '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let valueA = this.sortingDataAccessor(a, active);\n      let valueB = this.sortingDataAccessor(b, active);\n      // If there are data in the column that can be converted to a number,\n      // it must be ensured that the rest of the data\n      // is of the same type so as not to order incorrectly.\n      const valueAType = typeof valueA;\n      const valueBType = typeof valueB;\n      if (valueAType !== valueBType) {\n        if (valueAType === 'number') {\n          valueA += '';\n        }\n        if (valueBType === 'number') {\n          valueB += '';\n        }\n      }\n      // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n      // one value exists while the other doesn't. In this case, existing value should come last.\n      // This avoids inconsistent results when comparing values to undefined/null.\n      // If neither value exists, return 0 (equal).\n      let comparatorResult = 0;\n      if (valueA != null && valueB != null) {\n        // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n        if (valueA > valueB) {\n          comparatorResult = 1;\n        } else if (valueA < valueB) {\n          comparatorResult = -1;\n        }\n      } else if (valueA != null) {\n        comparatorResult = 1;\n      } else if (valueB != null) {\n        comparatorResult = -1;\n      }\n      return comparatorResult * (direction == 'asc' ? 1 : -1);\n    });\n  };\n  /**\n   * Checks if a data object matches the data source's filter string. By default, each data object\n   * is converted to a string of its properties and returns true if the filter has\n   * at least one occurrence in that string. By default, the filter string has its whitespace\n   * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n   * filter matching.\n   * @param data Data object used to check against the filter.\n   * @param filter Filter string that has been set on the data source.\n   * @returns Whether the filter matches against the data\n   */\n  filterPredicate = (data, filter) => {\n    // Transform the filter by converting it to lowercase and removing whitespace.\n    const transformedFilter = filter.trim().toLowerCase();\n    // Loops over the values in the array and returns true if any of them match the filter string\n    return Object.values(data).some(value => `${value}`.toLowerCase().includes(transformedFilter));\n  };\n  constructor(initialData = []) {\n    super();\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange = this._sort ? merge(this._sort.sortChange, this._sort.initialized) : of(null);\n    const pageChange = this._paginator ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized) : of(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    // Watched for paged data changes and send the result to the table to render.\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData = this.filter == null || this.filter === '' ? data : data.filter(obj => this.filterPredicate(obj, this.filter));\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n    return this.filteredData;\n  }\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data) {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n    return this.sortData(data.slice(), this.sort);\n  }\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data) {\n    if (!this.paginator) {\n      return data;\n    }\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n      if (!paginator) {\n        return;\n      }\n      paginator.length = filteredDataLength;\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA,kBAA2D;AAC3D,uBAA0B;AAxD1B,IAAM,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG;AACxD,IAAM,MAAM,CAAC,WAAW,iBAAiB,GAAG;AAC5C,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC,EAAE,GAAG,CAAC;AAChC,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,OAAO,OAAO;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,YAAY,GAAG;AAAA,EACnD;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,OAAO,OAAO;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,SAAS,OAAO,IAAI,GAAG,GAAG;AAAA,EAC3E;AACF;AAoBA,IAAM,YAAY,IAAI,eAAe,WAAW;AAEhD,IAAM,sBAAsB,IAAI,eAAe,qBAAqB;AAMpE,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA,EAEf,WAAW,OAAO,WAAW;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA,EAErB,WAAW,OAAO,WAAW;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA,EAErB,WAAW,OAAO,WAAW;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB;AAAA;AAAA,EAEpB,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,UAAU,KAAK,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,QAAI,UAAU,KAAK,YAAY;AAC7B,WAAK,aAAa;AAClB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,aAAa;AAAA;AAAA,EAEb;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,mBAAmB;AACjB,UAAM,mBAAmB,KAAK;AAC9B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,4BAA4B;AAC1B,SAAK,sBAAsB,CAAC,cAAc,KAAK,oBAAoB,EAAE;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,OAAO;AAGnB,QAAI,OAAO;AACT,WAAK,QAAQ;AACb,WAAK,uBAAuB,MAAM,QAAQ,iBAAiB,GAAG;AAC9D,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,gBAAgB,SAAS,4BAA4B,IAAI,KAAK,UAAU;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,YAAY,CAAC;AACzC,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAC/C,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,OAAO,GAAG;AAC3D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AAAA,MACnE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,gBAAgB,MAAM;AAAA,MAChC,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,IAC3D;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,WAAW,YAAY;AACjC,eAAW,cAAc,UAAU,IAAI,GAAG,UAAU,mBAAmB;AAAA,EACzE;AACF;AAEA,IAAM,gBAAN,MAAM,uBAAsB,YAAY;AAAA,EACtC,cAAc;AACZ,UAAM,OAAO,YAAY,GAAG,OAAO,UAAU,CAAC;AAAA,EAChD;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,MAAM,mBAAmB,EAAE,CAAC;AAAA,IAC9D,WAAW,CAAC,QAAQ,gBAAgB,GAAG,iBAAiB;AAAA,IACxD,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,gBAAN,MAAM,uBAAsB,YAAY;AAAA,EACtC,cAAc;AACZ,UAAM,YAAY,OAAO,YAAY;AACrC,UAAM,aAAa,OAAO,UAAU;AACpC,UAAM,WAAW,UAAU;AAC3B,UAAM,OAAO,UAAU,QAAQ,aAAa;AAC5C,QAAI,MAAM;AACR,iBAAW,cAAc,aAAa,QAAQ,IAAI;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,MAAM,mBAAmB,EAAE,CAAC;AAAA,IAC9D,WAAW,CAAC,GAAG,iBAAiB;AAAA,IAChC,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,UAAN,MAAM,iBAAgB,YAAY;AAAA,EAChC,cAAc;AACZ,UAAM,YAAY,OAAO,YAAY;AACrC,UAAM,aAAa,OAAO,UAAU;AACpC,UAAM,WAAW,UAAU;AAC3B,UAAM,OAAO,UAAU,QAAQ,aAAa;AAC5C,QAAI,MAAM;AACR,iBAAW,cAAc,aAAa,QAAQ,IAAI;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC;AAAA,IAChD,WAAW,CAAC,GAAG,UAAU;AAAA,IACzB,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,mBAAmB;AAKzB,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,WAAW,OAAO,WAAW;AAAA,EAC7B,WAAW,OAAO,eAAe;AAAA;AAAA,EAEjC;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,YAAY,SAAS;AAGnB,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,UAAU,QAAQ,SAAS,KAAK,QAAQ,SAAS,EAAE,gBAAgB,CAAC;AAC1E,WAAK,iBAAiB,KAAK,SAAS,KAAK,OAAO,EAAE,OAAO;AACzD,WAAK,eAAe,KAAK,OAAO;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK,eAAe,KAAK,KAAK,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,oBAAoB,QAAQ;AAC1B,QAAI,gBAAgB,iBAAiB;AACnC,aAAO,OAAO,WAAW;AAAA,IAC3B;AACA,QAAI,gBAAgB,iBAAiB;AACnC,aAAO,OAAO,WAAW;AAAA,IAC3B,OAAO;AACL,aAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,kBAAN,MAAM,yBAAwB,WAAW;AAAA,EACvC,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB;AAAA;AAAA,EAEpB,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,UAAU,KAAK,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc;AACZ,UAAM,OAAO,WAAW,GAAG,OAAO,eAAe,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA,EAGA,YAAY,SAAS;AACnB,UAAM,YAAY,OAAO;AAAA,EAC3B;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,mBAAmB,KAAK;AAC9B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,mBAAmB,SAAS;AAAA,MACzC,QAAQ,CAAC,GAAG,yBAAyB,UAAU,gBAAgB;AAAA,IACjE;AAAA,IACA,UAAU,CAAI,4BAA+B,oBAAoB;AAAA,EACnE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,kBAAN,MAAM,yBAAwB,WAAW;AAAA,EACvC,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB;AAAA;AAAA,EAEpB,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,UAAU,KAAK,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc;AACZ,UAAM,OAAO,WAAW,GAAG,OAAO,eAAe,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA,EAGA,YAAY,SAAS;AACnB,UAAM,YAAY,OAAO;AAAA,EAC3B;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,mBAAmB,KAAK;AAC9B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,mBAAmB,SAAS;AAAA,MACzC,QAAQ,CAAC,GAAG,yBAAyB,UAAU,gBAAgB;AAAA,IACjE;AAAA,IACA,UAAU,CAAI,4BAA+B,oBAAoB;AAAA,EACnE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,YAAN,MAAM,mBAAkB,WAAW;AAAA,EACjC,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD;AAAA,EACA,cAAc;AAGZ,UAAM,OAAO,WAAW,GAAG,OAAO,eAAe,CAAC;AAAA,EACpD;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,MAC1C,MAAM,CAAC,GAAG,iBAAiB,MAAM;AAAA,IACnC;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,iBAAiB,OAAO,gBAAgB;AAAA;AAAA,EAExC;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,uBAAuB;AAAA,EAC9B,cAAc;AACZ,mBAAc,uBAAuB;AAAA,EACvC;AAAA,EACA,cAAc;AAGZ,QAAI,eAAc,yBAAyB,MAAM;AAC/C,qBAAc,uBAAuB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,EACvC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,IAC5D,WAAW,CAAC,QAAQ,OAAO,GAAG,gBAAgB;AAAA,IAC9C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAAA,IAC9B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,IAC5D,WAAW,CAAC,QAAQ,OAAO,GAAG,gBAAgB;AAAA,IAC9C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAAA,IAC9B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAQ;AAAA,EAC3C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,GAAG,CAAC,MAAM,WAAW,EAAE,CAAC;AAAA,IAC9C,WAAW,CAAC,QAAQ,OAAO,GAAG,SAAS;AAAA,IACvC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAAA,IAC9B,UAAU,SAAS,gBAAgB,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,WAAW;AAAA,EAChC,oBAAoB;AAAA,EACpB,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,gBAAgB,EAAE,CAAC;AAAA,EACjD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,IAAM,oBAAoB,CAAC,OAAO,UAAU,QAAQ,OAAO;AAK3D,IAAM,eAAN,MAAmB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB,oBAAI,QAAQ;AAAA,EAC7B,kBAAkB,YAAY,iBAAiB,IAAI,WAAW,eAAe,aAAW,KAAK,mBAAmB,OAAO,CAAC,IAAI;AAAA,EAC5H,sCAAsC,CAAC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,oBAAoB,CAAC;AAAA,EACrB;AAAA,EACA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBb,YAAY,oBAAoB,eAAe,aAAa,MAAM,gCAAgC,MAAM,WAAW,mBAAmB,gBAAgB;AACpJ,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,gCAAgC;AACrC,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,MACpB,OAAO,GAAG,aAAa;AAAA,MACvB,UAAU,GAAG,aAAa;AAAA,MAC1B,QAAQ,GAAG,aAAa;AAAA,MACxB,SAAS,GAAG,aAAa;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,MAAM,kBAAkB;AAC7C,QAAI,iBAAiB,SAAS,MAAM,KAAK,iBAAiB,SAAS,OAAO,GAAG;AAC3E,WAAK,mCAAmC,IAAI;AAAA,IAC9C;AACA,UAAM,kBAAkB,CAAC;AACzB,eAAW,OAAO,MAAM;AAGtB,UAAI,IAAI,aAAa,IAAI,cAAc;AACrC;AAAA,MACF;AACA,sBAAgB,KAAK,KAAK,GAAG,MAAM,KAAK,IAAI,QAAQ,CAAC;AAAA,IACvD;AAEA,oBAAgB;AAAA,MACd,OAAO,MAAM;AACX,mBAAW,WAAW,iBAAiB;AACrC,eAAK,mBAAmB,SAAS,gBAAgB;AAAA,QACnD;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,oBAAoB,MAAM,mBAAmB,iBAAiB,wBAAwB,MAAM,SAAS,MAAM;AAEzG,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,cAAc,EAAE,kBAAkB,KAAK,WAAS,KAAK,KAAK,gBAAgB,KAAK,WAAS,KAAK,IAAI;AACzH,WAAK,mBAAmB,qBAAqB;AAAA,QAC3C,OAAO,CAAC;AAAA,MACV,CAAC;AACD,WAAK,mBAAmB,wBAAwB;AAAA,QAC9C,OAAO,CAAC;AAAA,MACV,CAAC;AACD;AAAA,IACF;AAEA,UAAM,WAAW,KAAK,CAAC;AACvB,UAAM,WAAW,SAAS,SAAS;AACnC,UAAM,QAAQ,KAAK,cAAc;AACjC,UAAM,QAAQ,QAAQ,UAAU;AAChC,UAAM,MAAM,QAAQ,SAAS;AAC7B,UAAM,kBAAkB,kBAAkB,YAAY,IAAI;AAC1D,UAAM,iBAAiB,gBAAgB,QAAQ,IAAI;AACnD,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,QAAQ;AACV,WAAK,+BAA+B;AAAA,QAClC,MAAM,CAAC,GAAG,IAAI;AAAA,QACd,mBAAmB,CAAC,GAAG,iBAAiB;AAAA,QACxC,iBAAiB,CAAC,GAAG,eAAe;AAAA,MACtC,CAAC;AAAA,IACH;AACA,oBAAgB;AAAA,MACd,WAAW,MAAM;AACf,qBAAa,KAAK,eAAe,UAAU,qBAAqB;AAChE,yBAAiB,KAAK,+BAA+B,YAAY,iBAAiB;AAClF,uBAAe,KAAK,6BAA6B,YAAY,eAAe;AAAA,MAC9E;AAAA,MACA,OAAO,MAAM;AACX,mBAAW,OAAO,MAAM;AACtB,mBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,kBAAM,OAAO,IAAI,SAAS,CAAC;AAC3B,gBAAI,kBAAkB,CAAC,GAAG;AACxB,mBAAK,gBAAgB,MAAM,OAAO,eAAe,CAAC,GAAG,MAAM,eAAe;AAAA,YAC5E;AACA,gBAAI,gBAAgB,CAAC,GAAG;AACtB,mBAAK,gBAAgB,MAAM,KAAK,aAAa,CAAC,GAAG,MAAM,cAAc;AAAA,YACvE;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,qBAAqB,WAAW,KAAK,OAAK,CAAC,CAAC,CAAC,GAAG;AACvD,eAAK,kBAAkB,qBAAqB;AAAA,YAC1C,OAAO,oBAAoB,KAAK,CAAC,IAAI,WAAW,MAAM,GAAG,kBAAkB,CAAC,EAAE,IAAI,CAAC,OAAO,UAAU,kBAAkB,KAAK,IAAI,QAAQ,IAAI;AAAA,UAC7I,CAAC;AACD,eAAK,kBAAkB,wBAAwB;AAAA,YAC7C,OAAO,mBAAmB,KAAK,CAAC,IAAI,WAAW,MAAM,cAAc,EAAE,IAAI,CAAC,OAAO,UAAU,gBAAgB,QAAQ,cAAc,IAAI,QAAQ,IAAI,EAAE,QAAQ;AAAA,UAC7J,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,aAAa,cAAc,UAAU;AAE7C,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AAIA,UAAM,OAAO,aAAa,WAAW,YAAY,MAAM,EAAE,QAAQ,IAAI;AACrE,UAAM,SAAS,aAAa,WAAW,aAAa,MAAM,EAAE,QAAQ,IAAI;AAExE,UAAM,gBAAgB,CAAC;AACvB,UAAM,oBAAoB,CAAC;AAC3B,UAAM,kBAAkB,CAAC;AAGzB,oBAAgB;AAAA,MACd,WAAW,MAAM;AACf,iBAAS,WAAW,GAAG,eAAe,GAAG,WAAW,KAAK,QAAQ,YAAY;AAC3E,cAAI,CAAC,OAAO,QAAQ,GAAG;AACrB;AAAA,UACF;AACA,wBAAc,QAAQ,IAAI;AAC1B,gBAAM,MAAM,KAAK,QAAQ;AACzB,0BAAgB,QAAQ,IAAI,KAAK,qBAAqB,MAAM,KAAK,IAAI,QAAQ,IAAI,CAAC,GAAG;AACrF,gBAAM,SAAS,KAAK,qBAAqB,GAAG,EAAE;AAC9C,0BAAgB;AAChB,4BAAkB,QAAQ,IAAI;AAAA,QAChC;AAAA,MACF;AAAA,MACA,OAAO,MAAM;AACX,cAAM,mBAAmB,OAAO,YAAY,IAAI;AAChD,iBAAS,WAAW,GAAG,WAAW,KAAK,QAAQ,YAAY;AACzD,cAAI,CAAC,OAAO,QAAQ,GAAG;AACrB;AAAA,UACF;AACA,gBAAM,SAAS,cAAc,QAAQ;AACrC,gBAAM,qBAAqB,aAAa;AACxC,qBAAW,WAAW,gBAAgB,QAAQ,GAAG;AAC/C,iBAAK,gBAAgB,SAAS,UAAU,QAAQ,kBAAkB;AAAA,UACpE;AAAA,QACF;AACA,YAAI,aAAa,OAAO;AACtB,eAAK,mBAAmB,wBAAwB;AAAA,YAC9C,OAAO;AAAA,YACP,SAAS;AAAA,YACT,UAAU;AAAA,UACZ,CAAC;AAAA,QACH,OAAO;AACL,eAAK,mBAAmB,wBAAwB;AAAA,YAC9C,OAAO;AAAA,YACP,SAAS;AAAA,YACT,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B,cAAc,cAAc;AACtD,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AAEA,oBAAgB;AAAA,MACd,OAAO,MAAM;AACX,cAAM,QAAQ,aAAa,cAAc,OAAO;AAChD,YAAI,OAAO;AACT,cAAI,aAAa,KAAK,WAAS,CAAC,KAAK,GAAG;AACtC,iBAAK,mBAAmB,OAAO,CAAC,QAAQ,CAAC;AAAA,UAC3C,OAAO;AACL,iBAAK,gBAAgB,OAAO,UAAU,GAAG,KAAK;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,6BAA6B;AACpC,mBAAa,KAAK,2BAA2B;AAAA,IAC/C;AACA,SAAK,iBAAiB,WAAW;AACjC,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,SAAS,kBAAkB;AAC5C,QAAI,CAAC,QAAQ,UAAU,SAAS,KAAK,aAAa,GAAG;AACnD;AAAA,IACF;AACA,eAAW,OAAO,kBAAkB;AAClC,cAAQ,MAAM,GAAG,IAAI;AACrB,cAAQ,UAAU,OAAO,KAAK,eAAe,GAAG,CAAC;AAAA,IACnD;AAKA,UAAM,eAAe,kBAAkB,KAAK,SAAO,iBAAiB,QAAQ,GAAG,MAAM,MAAM,QAAQ,MAAM,GAAG,CAAC;AAC7G,QAAI,cAAc;AAChB,cAAQ,MAAM,SAAS,KAAK,qBAAqB,OAAO;AAAA,IAC1D,OAAO;AAEL,cAAQ,MAAM,SAAS;AACvB,UAAI,KAAK,+BAA+B;AACtC,gBAAQ,MAAM,WAAW;AAAA,MAC3B;AACA,cAAQ,UAAU,OAAO,KAAK,aAAa;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,SAAS,KAAK,UAAU,iBAAiB;AACvD,YAAQ,UAAU,IAAI,KAAK,aAAa;AACxC,QAAI,iBAAiB;AACnB,cAAQ,UAAU,IAAI,KAAK,eAAe,GAAG,CAAC;AAAA,IAChD;AACA,YAAQ,MAAM,GAAG,IAAI,GAAG,QAAQ;AAChC,YAAQ,MAAM,SAAS,KAAK,qBAAqB,OAAO;AACxD,QAAI,KAAK,+BAA+B;AACtC,cAAQ,MAAM,WAAW;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,qBAAqB,SAAS;AAC5B,UAAM,mBAAmB;AAAA,MACvB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AACA,QAAI,SAAS;AAIb,eAAW,OAAO,mBAAmB;AACnC,UAAI,QAAQ,MAAM,GAAG,GAAG;AACtB,kBAAU,iBAAiB,GAAG;AAAA,MAChC;AAAA,IACF;AACA,WAAO,SAAS,GAAG,MAAM,KAAK;AAAA,EAChC;AAAA;AAAA,EAEA,eAAe,KAAK,wBAAwB,MAAM;AAChD,QAAI,CAAC,yBAAyB,KAAK,kBAAkB,QAAQ;AAC3D,aAAO,KAAK;AAAA,IACd;AACA,UAAM,aAAa,CAAC;AACpB,UAAM,gBAAgB,IAAI;AAC1B,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,YAAM,OAAO,cAAc,CAAC;AAC5B,iBAAW,KAAK,KAAK,qBAAqB,IAAI,EAAE,KAAK;AAAA,IACvD;AACA,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,+BAA+B,QAAQ,cAAc;AACnD,UAAM,YAAY,CAAC;AACnB,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,aAAa,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AACf,wBAAgB,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,QAAQ,cAAc;AACjD,UAAM,YAAY,CAAC;AACnB,QAAI,eAAe;AACnB,aAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACtC,UAAI,aAAa,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AACf,wBAAgB,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS;AAC5B,UAAM,aAAa,KAAK,eAAe,IAAI,OAAO;AAClD,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,UAAM,aAAa,QAAQ,sBAAsB;AACjD,UAAM,OAAO;AAAA,MACX,OAAO,WAAW;AAAA,MAClB,QAAQ,WAAW;AAAA,IACrB;AACA,QAAI,CAAC,KAAK,iBAAiB;AACzB,aAAO;AAAA,IACT;AACA,SAAK,eAAe,IAAI,SAAS,IAAI;AACrC,SAAK,gBAAgB,QAAQ,SAAS;AAAA,MACpC,KAAK;AAAA,IACP,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,+BAA+B,QAAQ;AACrC,SAAK,mCAAmC,OAAO,IAAI;AAEnD,QAAI,CAAC,KAAK,6BAA6B;AACrC,WAAK,oCAAoC,KAAK,MAAM;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,mCAAmC,MAAM;AACvC,UAAM,UAAU,IAAI,IAAI,IAAI;AAC5B,eAAW,UAAU,KAAK,qCAAqC;AAC7D,aAAO,OAAO,OAAO,KAAK,OAAO,SAAO,CAAC,QAAQ,IAAI,GAAG,CAAC;AAAA,IAC3D;AACA,SAAK,sCAAsC,KAAK,oCAAoC,OAAO,YAAU,CAAC,CAAC,OAAO,KAAK,MAAM;AAAA,EAC3H;AAAA;AAAA,EAEA,mBAAmB,SAAS;AAC1B,QAAI,oBAAoB;AACxB,eAAW,SAAS,SAAS;AAC3B,YAAM,WAAW,MAAM,eAAe,SAAS;AAAA,QAC7C,OAAO,MAAM,cAAc,CAAC,EAAE;AAAA,QAC9B,QAAQ,MAAM,cAAc,CAAC,EAAE;AAAA,MACjC,IAAI;AAAA,QACF,OAAO,MAAM,YAAY;AAAA,QACzB,QAAQ,MAAM,YAAY;AAAA,MAC5B;AACA,UAAI,SAAS,UAAU,KAAK,eAAe,IAAI,MAAM,MAAM,GAAG,SAAS,OAAO,MAAM,MAAM,GAAG;AAC3F,4BAAoB;AAAA,MACtB;AACA,WAAK,eAAe,IAAI,MAAM,QAAQ,QAAQ;AAAA,IAChD;AACA,QAAI,qBAAqB,KAAK,oCAAoC,QAAQ;AACxE,UAAI,KAAK,6BAA6B;AACpC,qBAAa,KAAK,2BAA2B;AAAA,MAC/C;AACA,WAAK,8BAA8B,WAAW,MAAM;AAClD,YAAI,KAAK,YAAY;AACnB;AAAA,QACF;AACA,mBAAW,UAAU,KAAK,qCAAqC;AAC7D,eAAK,oBAAoB,OAAO,MAAM,OAAO,mBAAmB,OAAO,iBAAiB,MAAM,KAAK;AAAA,QACrG;AACA,aAAK,sCAAsC,CAAC;AAC5C,aAAK,8BAA8B;AAAA,MACrC,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AACF;AACA,SAAS,OAAO,SAAS;AACvB,SAAO,CAAC,YAAY,mBAAmB,iBAAiB,EAAE,KAAK,WAAS,QAAQ,UAAU,SAAS,KAAK,CAAC;AAC3G;AAOA,SAAS,2BAA2B,IAAI;AACtC,SAAO,MAAM,kCAAkC,EAAE,IAAI;AACvD;AAKA,SAAS,iCAAiC,MAAM;AAC9C,SAAO,MAAM,+CAA+C,IAAI,IAAI;AACtE;AAKA,SAAS,sCAAsC;AAC7C,SAAO,MAAM,wGAA2G;AAC1H;AAKA,SAAS,mCAAmC,MAAM;AAChD,SAAO,MAAM,uEAA4E,KAAK,UAAU,IAAI,CAAC,EAAE;AACjH;AAKA,SAAS,8BAA8B;AACrC,SAAO,MAAM,qGAA0G;AACzH;AAKA,SAAS,iCAAiC;AACxC,SAAO,MAAM,wEAAwE;AACvF;AAKA,SAAS,4CAA4C;AACnD,SAAO,MAAM,6DAA6D;AAC5E;AAKA,SAAS,qCAAqC;AAC5C,SAAO,MAAM,qCAAqC;AACpD;AAGA,IAAM,8BAA8B,IAAI,eAAe,SAAS;AAMhE,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,eAAe,EAAE,GAAG,CAAC,SAAS,aAAa,IAAI,eAAe,EAAE,CAAC;AAAA,IAC3F,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,gBAAgB,OAAO,gBAAgB;AAAA,EACvC,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AACZ,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,aAAa;AACnB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,EACnC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,gBAAgB,OAAO,gBAAgB;AAAA,EACvC,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AACZ,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,mBAAmB;AACzB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACzC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,gBAAgB,OAAO,gBAAgB;AAAA,EACvC,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AACZ,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,mBAAmB;AACzB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACzC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,gBAAgB,OAAO,gBAAgB;AAAA,EACvC,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AACZ,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,mBAAmB;AACzB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACzC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,WAAW,OAAO,eAAe;AAAA,EACjC,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,gBAAgB,OAAO,uBAAuB;AAAA,EAC9C,iBAAiB,OAAO,aAAa;AAAA,EACrC,6BAA6B,OAAO,6BAA6B;AAAA,IAC/D,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B;AAAA;AAAA,EAEA,aAAa,IAAI,oBAAQ;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,iBAAiB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,uBAAuB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,uBAAuB,oBAAI,IAAI;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,+BAA+B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,8BAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAc9B,uBAAuB,oBAAI,IAAI;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,+BAA+B;AAAA;AAAA,EAE/B;AAAA;AAAA,EAEA,sBAAsB;AAAA;AAAA,EAEtB,iBAAiB;AAAA;AAAA,EAEjB,kBAAkB;AAAA;AAAA,EAElB,eAAe;AAEb,QAAI,KAAK,sBAAsB,QAAW;AAGxC,YAAM,YAAY,KAAK,YAAY,cAAc,aAAa,MAAM;AACpE,aAAO,cAAc,UAAU,cAAc,aAAa,aAAa;AAAA,IACzE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,IAAI;AACd,SAAK,OAAO,cAAc,eAAe,cAAc,MAAM,QAAQ,OAAO,OAAO,YAAY;AAC7F,cAAQ,KAAK,4CAA4C,KAAK,UAAU,EAAE,CAAC,GAAG;AAAA,IAChF;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,QAAI,KAAK,gBAAgB,YAAY;AACnC,WAAK,kBAAkB,UAAU;AAAA,IACnC;AAAA,EACF;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,sBAAsB,OAAO;AAC/B,SAAK,yBAAyB;AAG9B,QAAI,KAAK,cAAc,KAAK,WAAW,cAAc,QAAQ;AAC3D,WAAK,qBAAqB;AAC1B,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe;AAEpB,SAAK,8BAA8B;AACnC,SAAK,+BAA+B;AAAA,EACtC;AAAA,EACA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlC,aAAa,IAAI,4BAAgB;AAAA,IAC/B,OAAO;AAAA,IACP,KAAK,OAAO;AAAA,EACd,CAAC;AAAA;AAAA,EAED;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,OAAO,OAAO,IAAI,mBAAmB,MAAM,GAAG;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,CAAC,MAAM;AACT,WAAK,YAAY,cAAc,aAAa,QAAQ,OAAO;AAAA,IAC7D;AACA,SAAK,YAAY,CAAC,KAAK,UAAU;AACjC,SAAK,qBAAqB,KAAK,YAAY,cAAc,aAAa;AAItE,SAAK,cAAc,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,YAAY;AAChE,aAAO,KAAK,UAAU,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI,IAAI;AAAA,IACxE,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB;AACxB,SAAK,eAAe,OAAO,EAAE,SAAK,4BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC5E,WAAK,8BAA8B;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,wBAAwB;AAEtB,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ;AAC5B,KAAC,KAAK,YAAY,eAAe,KAAK,kBAAkB,eAAe,KAAK,kBAAkB,eAAe,KAAK,sBAAsB,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,sBAAsB,KAAK,sBAAsB,KAAK,iBAAiB,EAAE,QAAQ,SAAO;AAChR,WAAK,MAAM;AAAA,IACb,CAAC;AACD,SAAK,iBAAiB,CAAC;AACvB,SAAK,iBAAiB,CAAC;AACvB,SAAK,iBAAiB;AACtB,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,QAAI,aAAa,KAAK,UAAU,GAAG;AACjC,WAAK,WAAW,WAAW,IAAI;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAa;AACX,SAAK,cAAc,KAAK,kBAAkB;AAC1C,UAAM,UAAU,KAAK,YAAY,KAAK,KAAK,WAAW;AACtD,QAAI,CAAC,SAAS;AACZ,WAAK,iBAAiB;AACtB,WAAK,eAAe,KAAK;AACzB;AAAA,IACF;AACA,UAAM,gBAAgB,KAAK,WAAW;AACtC,SAAK,cAAc,aAAa,SAAS,eAAe,CAAC,QAAQ,wBAAwB,iBAAiB,KAAK,qBAAqB,OAAO,MAAM,YAAY,GAAG,YAAU,OAAO,KAAK,MAAM,YAAU;AACpM,UAAI,OAAO,cAAc,uBAAuB,YAAY,OAAO,SAAS;AAC1E,aAAK,2BAA2B,OAAO,OAAO,KAAK,QAAQ,OAAO,OAAO;AAAA,MAC3E;AAAA,IACF,CAAC;AAED,SAAK,uBAAuB;AAG5B,YAAQ,sBAAsB,YAAU;AACtC,YAAM,UAAU,cAAc,IAAI,OAAO,YAAY;AACrD,cAAQ,QAAQ,YAAY,OAAO,KAAK;AAAA,IAC1C,CAAC;AACD,SAAK,iBAAiB;AACtB,SAAK,eAAe,KAAK;AACzB,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA,EAEA,aAAa,WAAW;AACtB,SAAK,kBAAkB,IAAI,SAAS;AAAA,EACtC;AAAA;AAAA,EAEA,gBAAgB,WAAW;AACzB,SAAK,kBAAkB,OAAO,SAAS;AAAA,EACzC;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,SAAK,eAAe,IAAI,MAAM;AAAA,EAChC;AAAA;AAAA,EAEA,aAAa,QAAQ;AACnB,SAAK,eAAe,OAAO,MAAM;AAAA,EACnC;AAAA;AAAA,EAEA,gBAAgB,cAAc;AAC5B,SAAK,qBAAqB,IAAI,YAAY;AAC1C,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,mBAAmB,cAAc;AAC/B,SAAK,qBAAqB,OAAO,YAAY;AAC7C,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,gBAAgB,cAAc;AAC5B,SAAK,qBAAqB,IAAI,YAAY;AAC1C,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,mBAAmB,cAAc;AAC/B,SAAK,qBAAqB,OAAO,YAAY;AAC7C,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,aAAa,WAAW;AACtB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B;AAC5B,UAAM,aAAa,KAAK,iBAAiB,KAAK,gBAAgB;AAI9D,QAAI,KAAK,oBAAoB;AAC3B,YAAM,QAAQ,oBAAoB,KAAK,kBAAkB,OAAO;AAChE,UAAI,OAAO;AACT,cAAM,MAAM,UAAU,WAAW,SAAS,KAAK;AAAA,MACjD;AAAA,IACF;AACA,UAAM,eAAe,KAAK,eAAe,IAAI,SAAO,IAAI,MAAM;AAC9D,SAAK,cAAc,uBAAuB,YAAY,CAAC,KAAK,CAAC;AAC7D,SAAK,cAAc,UAAU,YAAY,cAAc,KAAK;AAE5D,SAAK,eAAe,QAAQ,SAAO,IAAI,mBAAmB,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B;AAC5B,UAAM,aAAa,KAAK,iBAAiB,KAAK,gBAAgB;AAI9D,QAAI,KAAK,oBAAoB;AAC3B,YAAM,QAAQ,oBAAoB,KAAK,kBAAkB,OAAO;AAChE,UAAI,OAAO;AACT,cAAM,MAAM,UAAU,WAAW,SAAS,KAAK;AAAA,MACjD;AAAA,IACF;AACA,UAAM,eAAe,KAAK,eAAe,IAAI,SAAO,IAAI,MAAM;AAC9D,SAAK,cAAc,uBAAuB,YAAY,CAAC,QAAQ,CAAC;AAChE,SAAK,cAAc,UAAU,YAAY,cAAc,QAAQ;AAC/D,SAAK,cAAc,4BAA4B,KAAK,YAAY,eAAe,YAAY;AAE3F,SAAK,eAAe,QAAQ,SAAO,IAAI,mBAAmB,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B;AACzB,UAAM,aAAa,KAAK,iBAAiB,KAAK,gBAAgB;AAC9D,UAAM,WAAW,KAAK,iBAAiB,KAAK,UAAU;AACtD,UAAM,aAAa,KAAK,iBAAiB,KAAK,gBAAgB;AAK9D,QAAI,KAAK,sBAAsB,CAAC,KAAK,gBAAgB,KAAK,8BAA8B;AAGtF,WAAK,cAAc,uBAAuB,CAAC,GAAG,YAAY,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC,QAAQ,OAAO,CAAC;AACxG,WAAK,+BAA+B;AAAA,IACtC;AAEA,eAAW,QAAQ,CAAC,WAAW,MAAM;AACnC,WAAK,uBAAuB,CAAC,SAAS,GAAG,KAAK,eAAe,CAAC,CAAC;AAAA,IACjE,CAAC;AAED,SAAK,SAAS,QAAQ,YAAU;AAE9B,YAAM,OAAO,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,KAAK,YAAY,CAAC,EAAE,WAAW,QAAQ;AACzC,eAAK,KAAK,SAAS,CAAC,CAAC;AAAA,QACvB;AAAA,MACF;AACA,WAAK,uBAAuB,MAAM,MAAM;AAAA,IAC1C,CAAC;AAED,eAAW,QAAQ,CAAC,WAAW,MAAM;AACnC,WAAK,uBAAuB,CAAC,SAAS,GAAG,KAAK,eAAe,CAAC,CAAC;AAAA,IACjE,CAAC;AAED,UAAM,KAAK,KAAK,kBAAkB,OAAO,CAAC,EAAE,QAAQ,SAAO,IAAI,mBAAmB,CAAC;AAAA,EACrF;AAAA;AAAA,EAEA,kBAAkB;AAMhB,QAAI,CAAC,KAAK,kBAAkB,KAAK,cAAc,KAAK,oBAAoB,KAAK,oBAAoB,KAAK,kBAAkB;AACtH,WAAK,iBAAiB;AAGtB,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,kBAAkB,KAAK;AAAA,EACrC;AAAA;AAAA,EAEA,UAAU;AAER,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAEtB,QAAI,CAAC,KAAK,eAAe,UAAU,CAAC,KAAK,eAAe,UAAU,CAAC,KAAK,SAAS,WAAW,OAAO,cAAc,eAAe,YAAY;AAC1I,YAAM,4BAA4B;AAAA,IACpC;AAEA,UAAM,iBAAiB,KAAK,sBAAsB;AAClD,UAAM,iBAAiB,kBAAkB,KAAK,wBAAwB,KAAK;AAE3E,SAAK,+BAA+B,KAAK,gCAAgC;AACzE,SAAK,8BAA8B;AAEnC,QAAI,KAAK,sBAAsB;AAC7B,WAAK,uBAAuB;AAC5B,WAAK,uBAAuB;AAAA,IAC9B;AAEA,QAAI,KAAK,sBAAsB;AAC7B,WAAK,uBAAuB;AAC5B,WAAK,uBAAuB;AAAA,IAC9B;AAGA,QAAI,KAAK,cAAc,KAAK,SAAS,SAAS,KAAK,CAAC,KAAK,2BAA2B;AAClF,WAAK,sBAAsB;AAAA,IAC7B,WAAW,KAAK,8BAA8B;AAG5C,WAAK,yBAAyB;AAAA,IAChC;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,UAAM,aAAa,CAAC;AAGpB,UAAM,uBAAuB,KAAK;AAClC,SAAK,uBAAuB,oBAAI,IAAI;AACpC,QAAI,CAAC,KAAK,OAAO;AACf,aAAO;AAAA,IACT;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,YAAM,oBAAoB,KAAK,sBAAsB,MAAM,GAAG,qBAAqB,IAAI,IAAI,CAAC;AAC5F,UAAI,CAAC,KAAK,qBAAqB,IAAI,IAAI,GAAG;AACxC,aAAK,qBAAqB,IAAI,MAAM,oBAAI,QAAQ,CAAC;AAAA,MACnD;AACA,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,YAAI,YAAY,kBAAkB,CAAC;AACnC,cAAM,QAAQ,KAAK,qBAAqB,IAAI,UAAU,IAAI;AAC1D,YAAI,MAAM,IAAI,UAAU,MAAM,GAAG;AAC/B,gBAAM,IAAI,UAAU,MAAM,EAAE,KAAK,SAAS;AAAA,QAC5C,OAAO;AACL,gBAAM,IAAI,UAAU,QAAQ,CAAC,SAAS,CAAC;AAAA,QACzC;AACA,mBAAW,KAAK,SAAS;AAAA,MAC3B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,MAAM,WAAW,OAAO;AAC5C,UAAM,UAAU,KAAK,YAAY,MAAM,SAAS;AAChD,WAAO,QAAQ,IAAI,YAAU;AAC3B,YAAM,mBAAmB,SAAS,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,CAAC;AAC3E,UAAI,iBAAiB,QAAQ;AAC3B,cAAM,UAAU,iBAAiB,MAAM;AACvC,gBAAQ,YAAY;AACpB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,kBAAkB,MAAM;AAC7B,UAAM,aAAa,iBAAiB,KAAK,YAAY,KAAK,kBAAkB,GAAG,KAAK,iBAAiB;AACrG,eAAW,QAAQ,eAAa;AAC9B,UAAI,KAAK,kBAAkB,IAAI,UAAU,IAAI,MAAM,OAAO,cAAc,eAAe,YAAY;AACjG,cAAM,iCAAiC,UAAU,IAAI;AAAA,MACvD;AACA,WAAK,kBAAkB,IAAI,UAAU,MAAM,SAAS;AAAA,IACtD,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,iBAAiB,iBAAiB,KAAK,YAAY,KAAK,qBAAqB,GAAG,KAAK,oBAAoB;AAC9G,SAAK,iBAAiB,iBAAiB,KAAK,YAAY,KAAK,qBAAqB,GAAG,KAAK,oBAAoB;AAC9G,SAAK,WAAW,iBAAiB,KAAK,YAAY,KAAK,eAAe,GAAG,KAAK,cAAc;AAE5F,UAAM,iBAAiB,KAAK,SAAS,OAAO,SAAO,CAAC,IAAI,IAAI;AAC5D,QAAI,CAAC,KAAK,yBAAyB,eAAe,SAAS,MAAM,OAAO,cAAc,eAAe,YAAY;AAC/G,YAAM,oCAAoC;AAAA,IAC5C;AACA,SAAK,iBAAiB,eAAe,CAAC;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,UAAM,qBAAqB,CAAC,KAAK,QAAQ;AAGvC,YAAM,OAAO,CAAC,CAAC,IAAI,eAAe;AAClC,aAAO,OAAO;AAAA,IAChB;AAEA,UAAM,qBAAqB,KAAK,SAAS,OAAO,oBAAoB,KAAK;AACzE,QAAI,oBAAoB;AACtB,WAAK,qBAAqB;AAAA,IAC5B;AAEA,UAAM,uBAAuB,KAAK,eAAe,OAAO,oBAAoB,KAAK;AACjF,QAAI,sBAAsB;AACxB,WAAK,uBAAuB;AAAA,IAC9B;AACA,UAAM,uBAAuB,KAAK,eAAe,OAAO,oBAAoB,KAAK;AACjF,QAAI,sBAAsB;AACxB,WAAK,uBAAuB;AAAA,IAC9B;AACA,WAAO,sBAAsB,wBAAwB;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,YAAY;AAC5B,SAAK,QAAQ,CAAC;AACd,QAAI,aAAa,KAAK,UAAU,GAAG;AACjC,WAAK,WAAW,WAAW,IAAI;AAAA,IACjC;AAEA,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,YAAY;AAC3C,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,CAAC,YAAY;AACf,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,KAAK,CAAC,CAAC;AAAA,MAC1B;AACA,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,cAAc,MAAM;AAAA,MACtC;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,wBAAwB;AAEtB,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,QAAI;AACJ,QAAI,aAAa,KAAK,UAAU,GAAG;AACjC,mBAAa,KAAK,WAAW,QAAQ,IAAI;AAAA,IAC3C,eAAW,0BAAa,KAAK,UAAU,GAAG;AACxC,mBAAa,KAAK;AAAA,IACpB,WAAW,MAAM,QAAQ,KAAK,UAAU,GAAG;AACzC,uBAAa,gBAAG,KAAK,UAAU;AAAA,IACjC;AACA,QAAI,eAAe,WAAc,OAAO,cAAc,eAAe,YAAY;AAC/E,YAAM,+BAA+B;AAAA,IACvC;AACA,SAAK,4BAA4B,WAAW,SAAK,4BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,UAAQ;AAC7F,WAAK,QAAQ,QAAQ,CAAC;AACtB,WAAK,WAAW;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AAEvB,QAAI,KAAK,iBAAiB,cAAc,SAAS,GAAG;AAClD,WAAK,iBAAiB,cAAc,MAAM;AAAA,IAC5C;AACA,SAAK,eAAe,QAAQ,CAAC,KAAK,MAAM,KAAK,WAAW,KAAK,kBAAkB,KAAK,CAAC,CAAC;AACtF,SAAK,4BAA4B;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AAEvB,QAAI,KAAK,iBAAiB,cAAc,SAAS,GAAG;AAClD,WAAK,iBAAiB,cAAc,MAAM;AAAA,IAC5C;AACA,SAAK,eAAe,QAAQ,CAAC,KAAK,MAAM,KAAK,WAAW,KAAK,kBAAkB,KAAK,CAAC,CAAC;AACtF,SAAK,4BAA4B;AAAA,EACnC;AAAA;AAAA,EAEA,uBAAuB,MAAM,QAAQ;AACnC,UAAM,aAAa,MAAM,KAAK,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI,gBAAc;AACrE,YAAM,YAAY,KAAK,kBAAkB,IAAI,UAAU;AACvD,UAAI,CAAC,cAAc,OAAO,cAAc,eAAe,YAAY;AACjE,cAAM,2BAA2B,UAAU;AAAA,MAC7C;AACA,aAAO;AAAA,IACT,CAAC;AACD,UAAM,oBAAoB,WAAW,IAAI,eAAa,UAAU,MAAM;AACtE,UAAM,kBAAkB,WAAW,IAAI,eAAa,UAAU,SAAS;AACvE,SAAK,cAAc,oBAAoB,MAAM,mBAAmB,iBAAiB,CAAC,KAAK,gBAAgB,KAAK,2BAA2B;AAAA,EACzI;AAAA;AAAA,EAEA,iBAAiB,WAAW;AAC1B,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,UAAU,cAAc,QAAQ,KAAK;AACvD,YAAM,UAAU,UAAU,cAAc,IAAI,CAAC;AAC7C,mBAAa,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,WAAW;AAC3B,QAAI,KAAK,SAAS,UAAU,GAAG;AAC7B,aAAO,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,IAC1B;AACA,QAAI,UAAU,CAAC;AACf,QAAI,KAAK,uBAAuB;AAC9B,gBAAU,KAAK,SAAS,OAAO,SAAO,CAAC,IAAI,QAAQ,IAAI,KAAK,WAAW,IAAI,CAAC;AAAA,IAC9E,OAAO;AACL,UAAI,SAAS,KAAK,SAAS,KAAK,SAAO,IAAI,QAAQ,IAAI,KAAK,WAAW,IAAI,CAAC,KAAK,KAAK;AACtF,UAAI,QAAQ;AACV,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,WAAW,OAAO,cAAc,eAAe,YAAY;AACtE,YAAM,mCAAmC,IAAI;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,WAAW,OAAO;AACrC,UAAM,SAAS,UAAU;AACzB,UAAM,UAAU;AAAA,MACd,WAAW,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,MACpB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,QAAQ,QAAQ,OAAO,UAAU,CAAC,GAAG;AAE9C,UAAM,OAAO,OAAO,cAAc,mBAAmB,OAAO,UAAU,SAAS,KAAK;AACpF,SAAK,2BAA2B,QAAQ,OAAO;AAC/C,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,QAAQ,SAAS;AAC1C,aAAS,gBAAgB,KAAK,kBAAkB,MAAM,GAAG;AACvD,UAAI,cAAc,sBAAsB;AACtC,sBAAc,qBAAqB,eAAe,mBAAmB,cAAc,OAAO;AAAA,MAC5F;AAAA,IACF;AACA,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,UAAM,gBAAgB,KAAK,WAAW;AACtC,aAAS,cAAc,GAAG,QAAQ,cAAc,QAAQ,cAAc,OAAO,eAAe;AAC1F,YAAM,UAAU,cAAc,IAAI,WAAW;AAC7C,YAAM,UAAU,QAAQ;AACxB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ,gBAAgB;AAChC,cAAQ,OAAO,gBAAgB,QAAQ;AACvC,cAAQ,OAAO,cAAc,MAAM;AACnC,cAAQ,MAAM,CAAC,QAAQ;AACvB,UAAI,KAAK,uBAAuB;AAC9B,gBAAQ,YAAY,KAAK,YAAY,WAAW,EAAE;AAClD,gBAAQ,cAAc;AAAA,MACxB,OAAO;AACL,gBAAQ,QAAQ,KAAK,YAAY,WAAW,EAAE;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,QAAQ;AACxB,QAAI,CAAC,UAAU,CAAC,OAAO,SAAS;AAC9B,aAAO,CAAC;AAAA,IACV;AACA,WAAO,MAAM,KAAK,OAAO,SAAS,cAAY;AAC5C,YAAM,SAAS,KAAK,kBAAkB,IAAI,QAAQ;AAClD,UAAI,CAAC,WAAW,OAAO,cAAc,eAAe,YAAY;AAC9D,cAAM,2BAA2B,QAAQ;AAAA,MAC3C;AACA,aAAO,OAAO,oBAAoB,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AACrB,SAAK,YAAY,KAAK,CAAC,CAAC;AACxB,SAAK,WAAW,cAAc,MAAM;AACpC,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,UAAM,qBAAqB,CAAC,KAAK,MAAM;AACrC,aAAO,OAAO,EAAE,iBAAiB;AAAA,IACnC;AAIA,QAAI,KAAK,eAAe,OAAO,oBAAoB,KAAK,GAAG;AACzD,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,KAAK,eAAe,OAAO,oBAAoB,KAAK,GAAG;AACzD,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,MAAM,KAAK,KAAK,kBAAkB,OAAO,CAAC,EAAE,OAAO,oBAAoB,KAAK,GAAG;AACjF,WAAK,+BAA+B;AACpC,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,UAAM,YAAY,KAAK,OAAO,KAAK,KAAK,QAAQ;AAChD,SAAK,gBAAgB,IAAI,aAAa,KAAK,oBAAoB,KAAK,gBAAgB,KAAK,UAAU,WAAW,KAAK,8BAA8B,WAAW,KAAK,4BAA4B,KAAK,SAAS;AAC3M,KAAC,KAAK,OAAO,KAAK,KAAK,aAAS,gBAAG,GAAG,SAAK,4BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AACxF,WAAK,cAAc,YAAY;AAC/B,WAAK,yBAAyB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,WAAO,MAAM,OAAO,UAAQ,CAAC,KAAK,UAAU,KAAK,WAAW,IAAI;AAAA,EAClE;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,YAAY,KAAK,oBAAoB,KAAK;AAChD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,UAAM,aAAa,KAAK,WAAW,cAAc,WAAW;AAC5D,QAAI,eAAe,KAAK,qBAAqB;AAC3C;AAAA,IACF;AACA,UAAM,YAAY,KAAK,iBAAiB;AACxC,QAAI,YAAY;AACd,YAAM,OAAO,UAAU,mBAAmB,UAAU,WAAW;AAC/D,YAAM,WAAW,KAAK,UAAU,CAAC;AAGjC,UAAI,KAAK,UAAU,WAAW,KAAK,UAAU,aAAa,KAAK,UAAU,cAAc;AACrF,iBAAS,aAAa,QAAQ,KAAK;AACnC,iBAAS,UAAU,IAAI,UAAU,iBAAiB;AAAA,MACpD;AAAA,IACF,OAAO;AACL,gBAAU,MAAM;AAAA,IAClB;AACA,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,GAAG,CAAC,SAAS,aAAa,EAAE,CAAC;AAAA,IACrD,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,cAAc,CAAC;AAC3C,QAAG,eAAe,UAAU,cAAc,CAAC;AAC3C,QAAG,eAAe,UAAU,WAAW,CAAC;AACxC,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,wBAAwB;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,wBAAwB;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,WAAW;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,uBAAuB,CAAC,GAAG,yBAAyB,yBAAyB,gBAAgB;AAAA,MAC7F,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IACjE;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAI,mBAAmB;AAAA,MAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,IAC3H,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,oBAAoB,GAAG,iCAAiC,GAAG,CAAC;AAC/D,QAAG,oBAAoB,GAAG,iCAAiC,GAAG,CAAC,EAAE,GAAG,iCAAiC,GAAG,CAAC;AAAA,MAC3G;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,EAAE;AACvC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,qBAAqB,IAAI,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB,eAAe,iBAAiB,eAAe;AAAA,IAC/E,QAAQ,CAAC,+CAA+C;AAAA,IACxD,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8BV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,kCAAkC;AAAA,MACpC;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW;AAAA,QAAC;AAAA,UACV,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QAEA;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC;AAAA,MACD,SAAS,CAAC,iBAAiB,eAAe,iBAAiB,eAAe;AAAA,MAC1E,QAAQ,CAAC,+CAA+C;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,iBAAiB,OAAO,KAAK;AACpC,SAAO,MAAM,OAAO,MAAM,KAAK,GAAG,CAAC;AACrC;AAKA,SAAS,oBAAoB,QAAQ,SAAS;AAC5C,QAAM,mBAAmB,QAAQ,YAAY;AAC7C,MAAI,UAAU,OAAO,cAAc,QAAQ;AAC3C,SAAO,SAAS;AAEd,UAAM,WAAW,QAAQ,aAAa,IAAI,QAAQ,WAAW;AAC7D,QAAI,aAAa,kBAAkB;AACjC,aAAO;AAAA,IACT,WAAW,aAAa,SAAS;AAE/B;AAAA,IACF;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO;AACT;AAWA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,SAAS,OAAO,UAAU;AAAA,IACxB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,qBAAqB;AAAA,IACrC,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AAGb,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA,EAEA,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK,YAAY,CAAC;AAAA,EACpC;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB;AACxB,QAAI,KAAK,eAAe,QAAW;AACjC,WAAK,aAAa,KAAK,yBAAyB;AAAA,IAClD;AACA,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,eAAe,KAAK,SAAS,wBAAwB,CAAC,MAAM,SAAS,KAAK,IAAI;AAAA,IACrF;AACA,QAAI,KAAK,QAAQ;AAIf,WAAK,UAAU,OAAO,KAAK;AAC3B,WAAK,UAAU,aAAa,KAAK;AACjC,WAAK,OAAO,aAAa,KAAK,SAAS;AAAA,IACzC,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,YAAM,0CAA0C;AAAA,IAClD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,gBAAgB,KAAK,SAAS;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AACzB,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC,SAAS,OAAO,cAAc,eAAe,YAAY;AAC5D,YAAM,mCAAmC;AAAA,IAC3C;AACA,QAAI,KAAK,YAAY,KAAK,SAAS,4BAA4B;AAC7D,aAAO,KAAK,SAAS,2BAA2B,IAAI;AAAA,IACtD;AACA,WAAO,KAAK,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,EAC7C;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,OAAO,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,SAAS,oBAAoB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,CAAC;AAC9B,QAAG,YAAY,YAAY,CAAC;AAC5B,QAAG,YAAY,kBAAkB,CAAC;AAAA,MACpC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,OAAO,GAAG;AAC3D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AAAA,MACnE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,mBAAmB,IAAI,GAAG,cAAc,GAAG,kBAAkB,GAAG,CAAC,YAAY,IAAI,GAAG,cAAc,GAAG,YAAY,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,YAAY,EAAE,CAAC;AAAA,IAC7L,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,wBAAwB,GAAG,CAAC;AAC/B,QAAG,WAAW,GAAG,6BAA6B,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,MAAM,CAAC;AAC1G,QAAG,sBAAsB;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,kBAAkB,eAAe,YAAY,OAAO;AAAA,IACjF,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,eAAe,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,cAAc,kBAAkB,eAAe,YAAY,OAAO;AAAA,IAC9E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB,CAAC,UAAU,WAAW,YAAY,eAAe,kBAAkB,kBAAkB,cAAc,SAAS,QAAQ,eAAe,eAAe,cAAc,iBAAiB,cAAc,iBAAiB,eAAe,iBAAiB,iBAAiB,eAAe,cAAc,gBAAgB,eAAe;AAC3V,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,UAAU,WAAW,YAAY,eAAe,kBAAkB,kBAAkB,cAAc,SAAS,QAAQ,eAAe,eAAe,cAAc,iBAAiB,cAAc,iBAAiB,eAAe,iBAAiB,iBAAiB,eAAe,cAAc,gBAAgB,eAAe;AAAA,IACvV,SAAS,CAAC,UAAU,WAAW,YAAY,eAAe,kBAAkB,kBAAkB,cAAc,SAAS,QAAQ,eAAe,eAAe,cAAc,iBAAiB,cAAc,iBAAiB,eAAe,iBAAiB,iBAAiB,eAAe,cAAc,gBAAgB,eAAe;AAAA,EACxU,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS,CAAC,iBAAiB,GAAG,qBAAqB;AAAA,IACrD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7xFH,IAAAA,eAAmE;AAEnE,IAAAC,oBAAoB;AAQpB,IAAMC,OAAM,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG;AACxD,IAAMC,OAAM,CAAC,WAAW,iBAAiB,GAAG;AAC5C,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC,EAAE,GAAG,CAAC;AAChC,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,OAAO,OAAO;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,YAAY,GAAG;AAAA,EACnD;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,OAAO,OAAO;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,SAAS,OAAO,IAAI,GAAG,GAAG;AAAA,EAC3E;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,eAAe,EAAE,GAAG,CAAC,SAAS,aAAa,IAAI,eAAe,EAAE,CAAC;AAAA,IAC3F,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,WAAN,MAAM,kBAAiB,SAAS;AAAA;AAAA,EAE9B,iBAAiB;AAAA;AAAA,EAEjB,+BAA+B;AAAA,EAC/B,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,iBAAiB,mBAAmB;AAClD,cAAQ,0BAA0B,wBAA2B,sBAAsB,SAAQ,IAAI,qBAAqB,SAAQ;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,GAAG,CAAC,SAAS,aAAa,EAAE,CAAC;AAAA,IACrD,WAAW,CAAC,GAAG,iBAAiB,uBAAuB;AAAA,IACvD,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,WAAW;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAI,mBAAmB;AAAA,MAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA;AAAA;AAAA,MAGA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,QAAQ,YAAY,GAAG,yBAAyB,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,IAC/K,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgBD,IAAG;AACtB,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,oBAAoB,GAAG,iCAAiC,GAAG,CAAC;AAC/D,QAAG,oBAAoB,GAAG,iCAAiC,GAAG,CAAC,EAAE,GAAG,iCAAiC,GAAG,CAAC;AAAA,MAC3G;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,EAAE;AACvC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,qBAAqB,IAAI,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB,eAAe,iBAAiB,eAAe;AAAA,IAC/E,QAAQ,CAAC,+mKAA+mK;AAAA,IACxnK,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8BV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,kCAAkC;AAAA,MACpC;AAAA,MACA,WAAW;AAAA,QAAC;AAAA,UACV,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA;AAAA;AAAA,QAGA;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QAEA;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC;AAAA,MACD,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,iBAAiB,eAAe,iBAAiB,eAAe;AAAA,MAC1E,QAAQ,CAAC,+mKAA+mK;AAAA,IAC1nK,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,aAAN,MAAM,oBAAmB,WAAW;AAAA,EAClC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,mBAAN,MAAM,0BAAyB,iBAAiB;AAAA,EAC9C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,mBAAN,MAAM,0BAAyB,iBAAiB;AAAA,EAC9C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA;AAAA,EAEtC,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B;AAC1B,UAAM,0BAA0B;AAChC,SAAK,oBAAoB,KAAK,cAAc,KAAK,oBAAoB,EAAE;AAAA,EACzE;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,gBAAgB,MAAM;AAAA,IAClC;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,MAAM,mBAAmB,EAAE,CAAC;AAAA,IAC9D,WAAW,CAAC,QAAQ,gBAAgB,GAAG,uBAAuB,6BAA6B;AAAA,IAC3F,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,MAAM,mBAAmB,EAAE,CAAC;AAAA,IAC9D,WAAW,CAAC,GAAG,uBAAuB,sBAAsB;AAAA,IAC5D,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,UAAN,MAAM,iBAAgB,QAAQ;AAAA,EAC5B,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gBAAgB,mBAAmB;AACjD,cAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,qBAAqB,QAAO;AAAA,IAC1H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC;AAAA,IAChD,WAAW,CAAC,GAAG,gBAAgB,sBAAsB;AAAA,IACrD,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,eAAe;AAKrB,IAAM,kBAAN,MAAM,yBAAwB,gBAAgB;AAAA,EAC5C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,mBAAmB,SAAS;AAAA,MACzC,QAAQ,CAAC,GAAG,yBAAyB,UAAU,gBAAgB;AAAA,IACjE;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,kBAAN,MAAM,yBAAwB,gBAAgB;AAAA,EAC5C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,mBAAmB,SAAS;AAAA,MACzC,QAAQ,CAAC,GAAG,yBAAyB,UAAU,gBAAgB;AAAA,IACjE;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA,EAChC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,MAC1C,MAAM,CAAC,GAAG,iBAAiB,MAAM;AAAA,IACnC;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACtC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,IAC5D,WAAW,CAAC,QAAQ,OAAO,GAAG,sBAAsB,4BAA4B;AAAA,IAChF,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAAA,IAC9B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACtC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,IAC5D,WAAW,CAAC,QAAQ,OAAO,GAAG,sBAAsB,qBAAqB;AAAA,IACzE,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAAA,IAC9B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,SAAN,MAAM,gBAAe,OAAO;AAAA,EAC1B,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,eAAe,mBAAmB;AAChD,cAAQ,wBAAwB,sBAAyB,sBAAsB,OAAM,IAAI,qBAAqB,OAAM;AAAA,IACtH;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,GAAG,CAAC,MAAM,WAAW,EAAE,CAAC;AAAA,IAC9C,WAAW,CAAC,QAAQ,OAAO,GAAG,eAAe,qBAAqB;AAAA,IAClE,UAAU,CAAC,QAAQ;AAAA,IACnB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAAA,IAC9B,UAAU,SAAS,gBAAgB,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACtC,oBAAoB;AAAA,EACpB,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,gBAAgB,EAAE,CAAC;AAAA,IAC/C,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,mBAAmB,IAAI,GAAG,cAAc,GAAG,kBAAkB,GAAG,CAAC,YAAY,IAAI,GAAG,cAAc,GAAG,YAAY,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,YAAY,EAAE,CAAC;AAAA,IAC7L,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,wBAAwB,GAAG,CAAC;AAC/B,QAAG,WAAW,GAAG,6BAA6B,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,MAAM,CAAC;AAC1G,QAAG,sBAAsB;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,kBAAkB,eAAe,YAAY,OAAO;AAAA,IACjF,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,eAAe,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,cAAc,kBAAkB,eAAe,YAAY,OAAO;AAAA,IAC9E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAME,yBAAwB;AAAA;AAAA,EAE9B;AAAA,EAAU;AAAA;AAAA,EAEV;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAY;AAAA,EAAW;AAAA,EAAkB;AAAA;AAAA,EAE1F;AAAA,EAAe;AAAA,EAAS;AAAA;AAAA,EAExB;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAc;AAAa;AAC/D,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS;AAAA,MAAC;AAAA,MAAiB;AAAA;AAAA,MAE3B;AAAA,MAAU;AAAA;AAAA,MAEV;AAAA,MAAkB;AAAA,MAAiB;AAAA,MAAc;AAAA,MAAY;AAAA,MAAW;AAAA,MAAkB;AAAA;AAAA,MAE1F;AAAA,MAAe;AAAA,MAAS;AAAA;AAAA,MAExB;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAc;AAAA,MAAc;AAAA,IAAa;AAAA,IAC/D,SAAS;AAAA,MAAC;AAAA;AAAA,MAEV;AAAA,MAAU;AAAA;AAAA,MAEV;AAAA,MAAkB;AAAA,MAAiB;AAAA,MAAc;AAAA,MAAY;AAAA,MAAW;AAAA,MAAkB;AAAA;AAAA,MAE1F;AAAA,MAAe;AAAA,MAAS;AAAA;AAAA,MAExB;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAc;AAAA,MAAc;AAAA,IAAa;AAAA,EACjE,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,gBAAgB,eAAe;AAAA,EAC5D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,gBAAgB,GAAGA,sBAAqB;AAAA,MACnE,SAAS,CAAC,iBAAiBA,sBAAqB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,mBAAmB;AAczB,IAAM,qBAAN,cAAiC,WAAW;AAAA;AAAA,EAE1C;AAAA;AAAA,EAEA,cAAc,IAAI,6BAAgB,CAAC,CAAC;AAAA;AAAA,EAEpC,UAAU,IAAI,6BAAgB,EAAE;AAAA;AAAA,EAEhC,uBAAuB,IAAI,qBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,MAAM;AACb,WAAO,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC;AACrC,SAAK,MAAM,KAAK,IAAI;AAGpB,QAAI,CAAC,KAAK,4BAA4B;AACpC,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,QAAQ,KAAK,MAAM;AAGxB,QAAI,CAAC,KAAK,4BAA4B;AACpC,WAAK,YAAY,KAAK,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AACb,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,WAAW;AACvB,SAAK,aAAa;AAClB,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,CAAC,MAAM,iBAAiB;AAC5C,UAAM,QAAQ,KAAK,YAAY;AAC/B,QAAI,eAAe,KAAK,GAAG;AACzB,YAAM,cAAc,OAAO,KAAK;AAGhC,aAAO,cAAc,mBAAmB,cAAc;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,CAAC,MAAM,SAAS;AACzB,UAAM,SAAS,KAAK;AACpB,UAAM,YAAY,KAAK;AACvB,QAAI,CAAC,UAAU,aAAa,IAAI;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,CAAC,GAAG,MAAM;AACzB,UAAI,SAAS,KAAK,oBAAoB,GAAG,MAAM;AAC/C,UAAI,SAAS,KAAK,oBAAoB,GAAG,MAAM;AAI/C,YAAM,aAAa,OAAO;AAC1B,YAAM,aAAa,OAAO;AAC1B,UAAI,eAAe,YAAY;AAC7B,YAAI,eAAe,UAAU;AAC3B,oBAAU;AAAA,QACZ;AACA,YAAI,eAAe,UAAU;AAC3B,oBAAU;AAAA,QACZ;AAAA,MACF;AAKA,UAAI,mBAAmB;AACvB,UAAI,UAAU,QAAQ,UAAU,MAAM;AAEpC,YAAI,SAAS,QAAQ;AACnB,6BAAmB;AAAA,QACrB,WAAW,SAAS,QAAQ;AAC1B,6BAAmB;AAAA,QACrB;AAAA,MACF,WAAW,UAAU,MAAM;AACzB,2BAAmB;AAAA,MACrB,WAAW,UAAU,MAAM;AACzB,2BAAmB;AAAA,MACrB;AACA,aAAO,oBAAoB,aAAa,QAAQ,IAAI;AAAA,IACtD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,CAAC,MAAM,WAAW;AAElC,UAAM,oBAAoB,OAAO,KAAK,EAAE,YAAY;AAEpD,WAAO,OAAO,OAAO,IAAI,EAAE,KAAK,WAAS,GAAG,KAAK,GAAG,YAAY,EAAE,SAAS,iBAAiB,CAAC;AAAA,EAC/F;AAAA,EACA,YAAY,cAAc,CAAC,GAAG;AAC5B,UAAM;AACN,SAAK,QAAQ,IAAI,6BAAgB,WAAW;AAC5C,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,4BAA4B;AAO1B,UAAM,aAAa,KAAK,YAAQ,oBAAM,KAAK,MAAM,YAAY,KAAK,MAAM,WAAW,QAAI,iBAAG,IAAI;AAC9F,UAAM,aAAa,KAAK,iBAAa,oBAAM,KAAK,WAAW,MAAM,KAAK,sBAAsB,KAAK,WAAW,WAAW,QAAI,iBAAG,IAAI;AAClI,UAAM,aAAa,KAAK;AAExB,UAAM,mBAAe,4BAAc,CAAC,YAAY,KAAK,OAAO,CAAC,EAAE,SAAK,uBAAI,CAAC,CAAC,IAAI,MAAM,KAAK,YAAY,IAAI,CAAC,CAAC;AAE3G,UAAM,kBAAc,4BAAc,CAAC,cAAc,UAAU,CAAC,EAAE,SAAK,uBAAI,CAAC,CAAC,IAAI,MAAM,KAAK,WAAW,IAAI,CAAC,CAAC;AAEzG,UAAM,oBAAgB,4BAAc,CAAC,aAAa,UAAU,CAAC,EAAE,SAAK,uBAAI,CAAC,CAAC,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC;AAEzG,SAAK,4BAA4B,YAAY;AAC7C,SAAK,6BAA6B,cAAc,UAAU,UAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM;AAIhB,SAAK,eAAe,KAAK,UAAU,QAAQ,KAAK,WAAW,KAAK,OAAO,KAAK,OAAO,SAAO,KAAK,gBAAgB,KAAK,KAAK,MAAM,CAAC;AAChI,QAAI,KAAK,WAAW;AAClB,WAAK,iBAAiB,KAAK,aAAa,MAAM;AAAA,IAChD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM;AAEf,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AACA,WAAO,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACd,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,UAAU,YAAY,KAAK,UAAU;AAC7D,WAAO,KAAK,MAAM,YAAY,aAAa,KAAK,UAAU,QAAQ;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,oBAAoB;AACnC,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,YAAY,KAAK;AACvB,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,gBAAU,SAAS;AAEnB,UAAI,UAAU,YAAY,GAAG;AAC3B,cAAM,gBAAgB,KAAK,KAAK,UAAU,SAAS,UAAU,QAAQ,IAAI,KAAK;AAC9E,cAAM,eAAe,KAAK,IAAI,UAAU,WAAW,aAAa;AAChE,YAAI,iBAAiB,UAAU,WAAW;AACxC,oBAAU,YAAY;AAGtB,eAAK,qBAAqB,KAAK;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,CAAC,KAAK,4BAA4B;AACpC,WAAK,0BAA0B;AAAA,IACjC;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,4BAA4B,YAAY;AAC7C,SAAK,6BAA6B;AAAA,EACpC;AACF;", "names": ["import_rxjs", "import_operators", "_c0", "_c1", "EXPORTED_DECLARATIONS"]}