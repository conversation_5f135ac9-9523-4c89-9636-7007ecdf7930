import { Component, computed, input, output } from '@angular/core';

import {MatIconModule} from '@angular/material/icon';
import {  MatSidenavModule } from '@angular/material/sidenav';
import { RouterModule } from '@angular/router';
import {MatListModule} from '@angular/material/list';
import {MatButtonModule} from '@angular/material/button';

import sidebarMenus from '@utils/data/sidebar-menus';
import { AuthService } from '@services/auth.service';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-sidebar',
  imports: [MatIconModule,MatListModule ,RouterModule,MatSidenavModule,MatButtonModule],
  templateUrl: './sidebar.html',
  styleUrl: './sidebar.scss'
})
export class Sidebar {
  onToggleSidebar = output<void>();
  sidebarCollapsed = input.required<boolean>();
  matIconClass = computed(() => !this.sidebarCollapsed() ? 'keyboard_tab' : 'keyboard_tab_rtl');
  sidebarHeaderClass = computed(() => !this.sidebarCollapsed() ? 'justify-center' : 'justify-between');

  sidebarMenus = sidebarMenus;

  constructor(private authService: AuthService, private toastService: ToastService) {}

  togglerClick(): void {
      this.onToggleSidebar.emit();
  }

  async onLogout(): Promise<void> {

      const toastRef = this.toastService.confirm("Logout",'Are you sure you logout?', 'Logout'); 
    toastRef.onAction().subscribe(async () => {
      await this.authService.logout();
      this.toastService.success('You’re signed out!!');
    });
  }
}
