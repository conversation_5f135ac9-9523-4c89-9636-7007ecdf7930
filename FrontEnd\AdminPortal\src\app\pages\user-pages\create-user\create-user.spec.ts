import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { CreateUser } from './create-user';
import { ToastService } from '@services/toast.service';
import { UserService } from '@services/user.service';

describe('CreateUser', () => {
  let component: CreateUser;
  let fixture: ComponentFixture<CreateUser>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockUserService: jasmine.SpyObj<UserService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', ['success', 'error']);
    const userServiceSpy = jasmine.createSpyObj('UserService', ['createUser']);

    await TestBed.configureTestingModule({
      imports: [
        CreateUser,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: UserService, useValue: userServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CreateUser);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockUserService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with step 0', () => {
    expect(component.currentStep).toBe(0);
    expect(component.steps[0].status).toBe('current');
    expect(component.steps[1].status).toBe('pending');
    expect(component.steps[2].status).toBe('pending');
  });

  it('should validate form fields correctly', () => {
    const form = component.userForm;
    
    // Test required fields
    expect(form.get('firstName')?.hasError('required')).toBeTruthy();
    expect(form.get('lastName')?.hasError('required')).toBeTruthy();
    expect(form.get('userName')?.hasError('required')).toBeTruthy();
    expect(form.get('email')?.hasError('required')).toBeTruthy();
    expect(form.get('password')?.hasError('required')).toBeTruthy();
    
    // Test email validation
    form.get('email')?.setValue('invalid-email');
    expect(form.get('email')?.hasError('email')).toBeTruthy();
    
    form.get('email')?.setValue('<EMAIL>');
    expect(form.get('email')?.hasError('email')).toBeFalsy();
  });

  it('should validate email match', () => {
    const form = component.userForm;
    
    form.get('email')?.setValue('<EMAIL>');
    form.get('confirmEmail')?.setValue('<EMAIL>');
    
    expect(form.hasError('emailMismatch')).toBeTruthy();
    
    form.get('confirmEmail')?.setValue('<EMAIL>');
    expect(form.hasError('emailMismatch')).toBeFalsy();
  });

  it('should validate password match', () => {
    const form = component.userForm;
    
    form.get('password')?.setValue('password123');
    form.get('confirmPassword')?.setValue('different123');
    
    expect(form.hasError('passwordMismatch')).toBeTruthy();
    
    form.get('confirmPassword')?.setValue('password123');
    expect(form.hasError('passwordMismatch')).toBeFalsy();
  });

  it('should navigate back to users list on cancel', () => {
    component.onCancel();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/user-management/admin-users']);
  });

  it('should navigate back to users list on back button', () => {
    component.onBackToUsers();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/user-management/admin-users']);
  });

  it('should show error when form is invalid on create user', () => {
    component.onCreateUser();
    expect(mockToastService.error).toHaveBeenCalledWith('Please fill in all required fields correctly.');
  });

  it('should update step status correctly', () => {
    component.currentStep = 1;
    component['updateStepStatus']();
    
    expect(component.steps[0].status).toBe('completed');
    expect(component.steps[1].status).toBe('current');
    expect(component.steps[2].status).toBe('pending');
  });

  it('should validate verification code format', () => {
    const verificationForm = component.verificationForm;
    
    // Test invalid codes
    verificationForm.get('verificationCode')?.setValue('123');
    expect(verificationForm.get('verificationCode')?.hasError('pattern')).toBeTruthy();
    
    verificationForm.get('verificationCode')?.setValue('12345a');
    expect(verificationForm.get('verificationCode')?.hasError('pattern')).toBeTruthy();
    
    // Test valid code
    verificationForm.get('verificationCode')?.setValue('123456');
    expect(verificationForm.get('verificationCode')?.hasError('pattern')).toBeFalsy();
  });
});
