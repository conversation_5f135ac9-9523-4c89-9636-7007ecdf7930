<div class="user-actions-modal">
      <div class="modal-header">
        <div class="header-content">
          <mat-icon class="material-symbols warning-icon">warning</mat-icon>
          <h2>User Actions</h2>
        </div>
        <button mat-icon-button (click)="onCancel()" class="close-button">
          <mat-icon class="material-symbols">close</mat-icon>
        </button>
      </div>

      <div class="modal-content">
        <p class="description">
          Choose an action for this user. Marking as inactive will preserve the user data while preventing access.
        </p>

        <div class="user-info">
          <h3>{{ data.user.name }}</h3>
          <p class="user-email">{{ data.user.email }}</p>
          <p class="user-status">Current Status: <span class="status-badge" [class]="getStatusClass(data.user.status)">{{ data.user.status }}</span></p>
        </div>
      </div>

      <div class="modal-actions"> 
        <button mat-button (click)="onMarkInactive()" class="inactive-button">
          <mat-icon class="material-symbols">person_off</mat-icon>
          Mark as Inactive
        </button>
        <button mat-button (click)="onDeletePermanently()" class="delete-button">
          <mat-icon class="material-symbols">delete_forever</mat-icon>
          Delete Permanently
        </button>
      </div>
    </div>