import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { Environment } from '../../environments/environment.interface';

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {
  private readonly env: Environment = environment;

  get environment(): Environment {
    return this.env;
  }

  get isProduction(): boolean {
    return this.env.production;
  }

  get isDevelopment(): boolean {
    return !this.env.production;
  }

  get apiBaseUrl(): string {
    return this.env.apiBaseUrl;
  }

  get currentEnvironment(): string {
    return this.env.environment;
  }

  get isLoggingEnabled(): boolean {
    return this.env.enableLogging;
  }

  get isDebugModeEnabled(): boolean {
    return this.env.enableDebugMode;
  }

  get port(): number {
    return this.env.port;
  }

  /**
   * Log message only if logging is enabled for current environment
   */
  log(message: any, ...optionalParams: any[]): void {
    if (this.isLoggingEnabled) {
      console.log(`[${this.currentEnvironment.toUpperCase()}]`, message, ...optionalParams);
    }
  }

  /**
   * Log error message only if logging is enabled for current environment
   */
  error(message: any, ...optionalParams: any[]): void {
    if (this.isLoggingEnabled) {
      console.error(`[${this.currentEnvironment.toUpperCase()}]`, message, ...optionalParams);
    }
  }

  /**
   * Log debug message only if debug mode is enabled for current environment
   */
  debug(message: any, ...optionalParams: any[]): void {
    if (this.isDebugModeEnabled) {
      console.debug(`[${this.currentEnvironment.toUpperCase()}]`, message, ...optionalParams);
    }
  }
}
