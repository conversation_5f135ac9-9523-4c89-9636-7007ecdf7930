<div class="reset-password-modal">
  <!-- <PERSON><PERSON> -->
  <div class="modal-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon material-symbols">lock_reset</mat-icon>
      Reset Password
    </h2>
    <button mat-icon-button mat-dialog-close class="close-button">
      <mat-icon class="material-symbols">close</mat-icon>
    </button>
  </div>

  <!-- User Info -->
  <div class="user-info">
    <div class="user-details">
      <mat-icon class="user-icon material-symbols">person</mat-icon>
      <div class="user-text">
        <h3>{{ data.user.name }}</h3>
        <p>{{ data.user.email }}</p>
      </div>
    </div>
  </div>

  <!-- Step Content -->
  <div class="modal-content" mat-dialog-content>
    
    <!-- Step 1: OTP Verification -->
    @if (currentStep() === 0) {
      <div class="step-container">
        <div class="step-header">
          <div class="step-indicator">
            <span class="step-number">1</span>
          </div>
          <div class="step-info">
            <h3>Verify Identity</h3>
            <p>We've sent a 6-digit verification code to <strong>{{ data.user.email }}</strong></p>
          </div>
        </div>

        <form [formGroup]="otpForm" class="otp-form">
          <mat-form-field appearance="outline"  class="w-full">
            <mat-label>Verification Code</mat-label>
            <input
              matInput
              formControlName="otp"
              placeholder="Enter 6-digit code"
              maxlength="6"
              autocomplete="one-time-code"
              inputmode="numeric"
              pattern="[0-9]*"
              (keyup.enter)="onVerifyOtp()">
            <mat-icon matSuffix class="material-symbols">security</mat-icon>
            @if (getOtpError()) {
              <mat-error>{{ getOtpError() }}</mat-error>
            }
          </mat-form-field> 
        </form>

      </div>
    }

    <!-- Step 2: Set New Password -->
    @if (currentStep() === 1) {
      <div class="step-container">
        <div class="step-header">
          <div class="step-indicator completed">
            <mat-icon class="material-symbols">check</mat-icon>
          </div>
          <div class="step-info">
            <h3>Set New Password</h3>
            <p>Create a strong password for <strong>{{ data.user.name }}</strong></p>
          </div>
        </div>

        <form [formGroup]="passwordForm" class="password-form">
          <mat-form-field appearance="outline" class="password-field">
            <mat-label>New Password</mat-label>
            <input 
              matInput 
              formControlName="password" 
              type="password"
              placeholder="Enter new password"
              autocomplete="new-password">
            <mat-icon matSuffix class="material-symbols">lock</mat-icon>
            @if (getPasswordError()) {
              <mat-error>{{ getPasswordError() }}</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="password-field">
            <mat-label>Confirm Password</mat-label>
            <input 
              matInput 
              formControlName="confirmPassword" 
              type="password"
              placeholder="Confirm new password"
              autocomplete="new-password"
              (keyup.enter)="onResetPassword()">
            <mat-icon matSuffix class="material-symbols">lock</mat-icon>
            @if (getConfirmPasswordError()) {
              <mat-error>{{ getConfirmPasswordError() }}</mat-error>
            }
          </mat-form-field>
 
        </form>
 
      </div>
    }
  </div>

  <!-- Modal Actions -->
  <div class="modal-actions" mat-dialog-actions>
    @if (currentStep() === 0) {
            <button 
              mat-button 
              type="button"
              (click)="onResendOtp()"
              [disabled]="isResendDisabled()"
              class="resend-button">
              {{ resendButtonText() }}
            </button>

            <app-primary-button 
              mat-raised-button 
              [type]="'button'"
              (buttonClick)="onVerifyOtp()"
              [loading] = "isLoading()"
              [disabled]="!isOtpValid()"
              [text]="'Verify Otp'"
              />  
    }
    @if(currentStep() === 1)
    {
      <button 
            mat-button 
            type="button"
            (click)="onBackToOtp()"
            [disabled]="isLoading()">
            <mat-icon class="material-symbols">arrow_back</mat-icon>
            Back to Verification
          </button> 
          <app-primary-button 
              mat-raised-button 
              [type]="'button'"
              (buttonClick)="onResetPassword()"
              [loading] = "isLoading()"
              [disabled]="!isPasswordValid() || isLoading()"
              [text]="'Reset Password'"
              />  
    }
  </div> 
</div>
