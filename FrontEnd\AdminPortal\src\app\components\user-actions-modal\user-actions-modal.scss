.user-actions-modal {
  width: 480px;
  max-width: 90vw;
  background: white;
  border-radius: 12px;
  overflow: hidden;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #e0e0e0;

    .header-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .warning-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
        color: #ff9800;
      }

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }
    }

    .close-button {
      color: #666;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  .modal-content {
    padding: 24px;

    .description {
      margin: 0 0 24px 0;
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }

    .user-info {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px; 
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .user-email {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #666; 
      }

      .user-status {
        margin: 0;
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 8px;

        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;

          &.status-active {
            background-color: #e8f5e8;
            color: #2e7d32;
          }

          &.status-inactive {
            background-color: #fff3e0;
            color: #f57c00;
          }

          &.status-pending {
            background-color: #e3f2fd;
            color: #1976d2;
          }

          &.status-deactivated {
            background-color: #ffebee;
            color: #d32f2f;
          }

          &.status-locked {
            background-color: #fce4ec;
            color: #c2185b;
          }

          &.status-default {
            background-color: #f5f5f5;
            color: #666;
          }
        }
      }
    }
  }

  .modal-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px 24px 24px 24px;
    border-top: 1px solid #e0e0e0;

    .cancel-button {
      color: #666;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .inactive-button {
      background-color: #ff9800;
      color: white;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        background-color: #f57c00;
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .delete-button {
      background-color: #f44336;
      color: white;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        background-color: #d32f2f;
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Global dialog styles
::ng-deep .user-actions-dialog {
  .mat-mdc-dialog-container {
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .mat-mdc-dialog-surface {
    border-radius: 12px;
  }
}

// Responsive design
@media (max-width: 600px) {
  .user-actions-modal {
    width: 100%;
    max-width: calc(100vw - 32px);

    .modal-header {
      padding: 20px 20px 12px 20px;

      .header-content {
        h2 {
          font-size: 18px;
        }
      }
    }

    .modal-content {
      padding: 20px;

      .user-info {
        padding: 16px;

        h3 {
          font-size: 16px;
        }
      }
    }

    .modal-actions {
      padding: 12px 20px 20px 20px;
      flex-direction: column;
      align-items: stretch;
      gap: 8px;

      .cancel-button,
      .inactive-button,
      .delete-button {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
