import { Injectable, signal, computed, PLATFORM_ID, Inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { LoginModel, LoginResponseModel } from '@models/auth-model/LoginModel';
import { ForgotPasswordModel } from '@models/auth-model/ForgotPasswordModel';
import { VerifyOtpModel, VerifyOtpResponseModel } from '@models/auth-model/VerifyOtpModel';
import { ResetPasswordModel } from '@models/auth-model/ResetPasswordModel';
import { MfaEnableModel, MfaSetupResponseModel} from '@models/user-model/MfaModel';
import { ApiBaseResponse } from '@models/ApiResponse';
import { environment } from '../../environments/environment';
import { BaseService } from './base.service';
import { Utils } from '@utils/common/utils';
import { User } from '@models/user-model/UserModel';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly STORAGE_KEY = 'invofy_auth_token';
  private readonly USER_KEY = 'invofy_user_data';

 
  private _isAuthenticated = signal<boolean>(false);
  private _currentUser = signal<User | null>(null);
  private _token = signal<string | null>(null);
  private _isLoading = signal<boolean>(false);
 
  isAuthenticated = computed(() => this._isAuthenticated());
  currentUser = computed(() => this._currentUser());
  isLoading = computed(() => this._isLoading());

  constructor(
    private http: HttpClient,
    private _baseService: BaseService,
    private _utils: Utils,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initializeAuthState();
  } 

  async login(credentials: LoginModel): Promise<ApiBaseResponse<LoginResponseModel>> {
    this._isLoading.set(true); 
    try {
      const response = await this._baseService.post<LoginResponseModel>("Auth/Login", credentials);
      
      if (response?.isSuccess && response.data) {
        this.setUserData(response); 
        this._isLoading.set(false);
        return response;
      } else {
        this._isLoading.set(false);
        return response;
      }
    } catch (error) {
      this._isLoading.set(false);

      if (error instanceof HttpErrorResponse) { 
        if (error.status === 401) {
          return {
            isSuccess: false,
            message: 'Invalid username or password',
            stateCode: error.status
          };
        } else if (error.status === 0) {
          return {
            isSuccess: false,
            message: 'Unable to connect to server. Please check your connection.',
            stateCode: error.status

          };
        } else {
          return {
            isSuccess: false,
            message: error.error?.message || 'An error occurred during login',
            stateCode: error.status
          };
        }
      }

      return {
        isSuccess: false,
        message: 'An unexpected error occurred during login',
        stateCode: 500
      };
    }
  }

  async verifyOtp(otpData: VerifyOtpModel): Promise<{ success: boolean; message: string; token?: string }> {
    try { 
      const response = await this._baseService.post<VerifyOtpResponseModel>("Auth/VerifyPasswordResetOtp", otpData);
      return {
        success: response.isSuccess,
        message: response.message || 'OTP verified successfully',
        token: response.data?.token
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        // Handle HTTP errors
        if (error.status === 400) {
          return {
            success: false,
            message: 'Invalid or expired OTP'
          };
        } else if (error.status === 404) {
          return {
            success: false,
            message: 'Email address not found'
          };
        } else if (error.status === 0) {
          return {
            success: false,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            success: false,
            message: error.error?.message || 'An error occurred while verifying OTP'
          };
        }
      }

      return {
        success: false,
        message: 'An unexpected error occurred while verifying OTP'
      };
    }
  }

  async verifyTwoFactor(otp: string): Promise<ApiBaseResponse<LoginResponseModel>> {
    try {
      if (!this.currentUser() || !this.currentUser()?.email) {
        return {
          isSuccess: false,
          message: 'User email not found. Please login again.',
          stateCode: 401
        };
      } 
      
      const response = await this._baseService.post<LoginResponseModel>("Auth/verifyTwoFactor",  {
            email: this.currentUser()?.email,
            otp: otp
          }); 

      if (response && response.isSuccess && response.data) {
        this.setUserData(response);
        this._isLoading.set(false);
        return response;
      } else {
        this._isLoading.set(false);
        return response;
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        // Handle HTTP errors
        if (error.status === 400) {
          return {
            isSuccess: false,
            stateCode: error.status,
            message: 'Invalid or expired OTP',
          };
        } else if (error.status === 404) {
          return {
            isSuccess: false,
            stateCode: error.status,
            message: 'Email address not found'
          };
        } else if (error.status === 0) {
          return {
            isSuccess: false,
            stateCode: error.status,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            isSuccess: false,
            stateCode: error.status,
            message: error.error?.message || 'An error occurred while verifying OTP'
          };
        }
      }

      return {
        isSuccess: false,
        stateCode: 500,
        message: 'An unexpected error occurred while verifying OTP'
      };
    }
  }

  async resetPassword(resetData: ResetPasswordModel): Promise<{ success: boolean; message: string }> {
    try {
        const response = await this._baseService.post<any>("Auth/ResetPassword",  resetData);

      return {
        success: response.isSuccess,
        message: response.message || 'Password reset successfully'
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) { 
        if (error.status === 400) {
          return {
            success: false,
            message: 'Invalid or expired reset token'
          };
        } else if (error.status === 422) {
          return {
            success: false,
            message: 'Password does not meet requirements'
          };
        } else if (error.status === 0) {
          return {
            success: false,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            success: false,
            message: error.error?.message || 'An error occurred while resetting password'
          };
        }
      }

      return {
        success: false,
        message: 'An unexpected error occurred while resetting password'
      };
    }
  }

  async forgotPassword(email: ForgotPasswordModel): Promise<{ success: boolean; message: string }> {
    try {

        const response = await this._baseService.post<any>("Auth/ForgotPassword",  email);
      return {
        success: response.isSuccess,
        message: response.message || 'Password reset email sent successfully'
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) { 
        if (error.status === 404) {
          return {
            success: false,
            message: 'Email address not found'
          };
        } else if (error.status === 0) {
          return {
            success: false,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            success: false,
            message: error.error?.message || 'An error occurred while sending reset email'
          };
        }
      }

      return {
        success: false,
        message: 'An unexpected error occurred while sending reset email'
      };
    }
  } 

  async setupTwoFactor(email: string): Promise<{ success: boolean; qrCode?: string; secretKey?: string; message: string }> {
    try {

        const response = await this._baseService.post<any>(`Auth/SetupTwoFactor?email=${email}`,  null);
 
      return {
        success: response.isSuccess,
        qrCode: response.data?.qrCodeBase64Image,
        secretKey: response.data?.secretKey,
        message: response.message || 'Two-factor authentication setup initiated'
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        return {
          success: false,
          message: error.error?.message || 'Failed to setup two-factor authentication'
        };
      }
      return {
        success: false,
        message: 'An unexpected error occurred during two-factor authentication setup'
      };
    }
  }

  async enableTwoFactor(enableData: MfaEnableModel): Promise<{ success: boolean; message: string }> {
    try {
        const response = await this._baseService.post<any>(`Auth/EnableTwoFactor`,  enableData);
 
      return {
        success: response.isSuccess,
        message: response.message || 'Two-factor authentication enabled successfully'
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        return {
          success: false,
          message: error.error?.message || 'Failed to enable two-factor authentication'
        };
      }
      return {
        success: false,
        message: 'An unexpected error occurred while enabling two-factor authentication'
      };
    }
  }

  
  async logout(): Promise<void> {
    this.clearAuthState(); 
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.router.navigate(['/auth/login']);
  } 
  
  getToken(): string | null {
    return this._token();
  }

  private clearAuthState(): void {
    this._isAuthenticated.set(false);
    this._currentUser.set(null);
    this._token.set(null); 
    if (this._utils.isBrowser()) {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }


  private setUserData(response: ApiBaseResponse<LoginResponseModel>) {
    if (response && response.data) {

      const { token, user } = response.data;
      this._token.set(token);
      this._currentUser.set(user);
      if (token)
        this._isAuthenticated.set(true);

      // Persist to localStorage (only in browser)
      if (this._utils.isBrowser()) {
        localStorage.setItem(this.STORAGE_KEY, token);
        localStorage.setItem(this.USER_KEY, JSON.stringify(user));
      }
    }
  }

  
  private initializeAuthState(): void {
    if (!this._utils.isBrowser()) {
      return; 
    }

    try {
      const token = localStorage.getItem(this.STORAGE_KEY);
      const userData = localStorage.getItem(this.USER_KEY);

      if (token && userData) {
        const user = JSON.parse(userData);
        this._token.set(token);
        this._currentUser.set(user);
        if (token)
          this._isAuthenticated.set(true);
      }

    } catch (error) {
      console.error('Error initializing auth state:', error);
      this.clearAuthState();
    }
  }

}
