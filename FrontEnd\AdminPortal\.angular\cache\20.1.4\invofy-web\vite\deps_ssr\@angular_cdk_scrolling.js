import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory
} from "./chunk-FJSIZZGE.js";
import "./chunk-YRVB5RKG.js";
import "./chunk-YZRXDCC3.js";
import {
  Dir
} from "./chunk-5AU7KZXI.js";
import "./chunk-53SREPDD.js";
import "./chunk-4XXXTM2K.js";
import "./chunk-PPO2RDEN.js";
import "./chunk-WCSRJUZM.js";
import "./chunk-2XLRDDJW.js";
import "./chunk-43KPLV43.js";
import "./chunk-TXGYY7YM.js";
import "./chunk-6DU2HRTW.js";
export {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory,
  Dir as ɵɵDir
};
