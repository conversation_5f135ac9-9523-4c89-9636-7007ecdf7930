import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

export interface StepItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  status: 'completed' | 'current' | 'pending';
}

@Component({
  selector: 'app-step-progress',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './step-progress.html',
  styleUrls: ['./step-progress.scss']
})
export class StepProgress {
  @Input() steps: StepItem[] = [];
  @Input() currentStep: number = 0;
}
