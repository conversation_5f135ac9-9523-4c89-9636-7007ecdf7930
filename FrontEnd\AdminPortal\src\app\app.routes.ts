import { Routes } from '@angular/router';
import { Login } from '@pages/auth-pages/login/login';
import { AuthLayout } from '@layouts/auth-layout/auth-layout';
import { AppLayout } from '@layouts/app-layout/app-layout';
import { Mfa } from '@pages/auth-pages/mfa/mfa';
import { MfaSetup } from '@pages/user-pages/mfa-setup/mfa-setup';
import { ForgotPassword } from '@pages/auth-pages/forgot-password/forgot-password';
import { VerifyOtp } from '@pages/auth-pages/verify-otp/verify-otp';
import { ResetPassword } from '@pages/auth-pages/reset-password/reset-password';
import { Dashboard } from '@pages/dashboard/dashboard';
import { AuthGuard } from '@guards/auth.guard';
import { GuestGuard } from '@guards/guest.guard';
import { AdminUsers } from '@pages/user-pages/admin-users/admin-users';
import { CreateUser } from '@pages/user-pages/create-user/create-user';
import { EditUser } from '@pages/user-pages/edit-user/edit-user';
import { Stores } from '@pages/store-pages/stores/stores';

export const routes: Routes = [
    {
        path: '',
        component: AppLayout,
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        children: [
            { path: 'invoices', component: Dashboard },
            { path: 'items', component: Dashboard },
            { path: 'reports', component: Dashboard },
            { path: 'store-setting', component: Dashboard },
            { path: 'mfa-setup', component: MfaSetup },
            {
                path: 'admin-user',
                children: [
                    { path: '', component: AdminUsers },
                    { path: 'add', component: CreateUser },
                    { path: 'edit', component: EditUser },
                ]
            },
            {
                path: 'stores',
                children: [
                    { path: '', component: Stores },
                    // Future routes for add/edit stores can be added here
                ]
            },
            { path: '', pathMatch: 'full', component: Dashboard },
        ],
    },
    {
        path: 'auth',
        component: AuthLayout,
        canActivate: [GuestGuard],
        canActivateChild: [GuestGuard],
        children: [
            { path: 'login', component: Login },
            { path: 'mfa', component: Mfa },
            { path: 'forgot-password', component: ForgotPassword },
            { path: 'verify-otp', component: VerifyOtp },
            { path: 'reset-password', component: ResetPassword },
            { path: '', redirectTo: 'login', pathMatch: 'full' },
        ],
    },
];
