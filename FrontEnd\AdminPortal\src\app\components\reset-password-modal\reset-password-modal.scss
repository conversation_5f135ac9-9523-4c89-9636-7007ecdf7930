.reset-password-modal {
  width: 100%;
  max-width: 500px;
  position: relative;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #e0e0e0;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;

      .header-icon {
        color: var(--ivy-sky-blue, #2196f3);
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }

    .close-button {
      color: #666;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  .user-info {
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .user-details {
      display: flex;
      align-items: center;
      gap: 12px;

      .user-icon {
        color: #666;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .user-text {
        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .modal-content {
    padding: 24px; 
  }

  .step-container {
    .step-header {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 24px;

      .step-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--ivy-sky-blue, #2196f3);
        color: white;
        font-weight: 600;
        flex-shrink: 0;

        &.completed {
          background-color: #4caf50;
        }

        .step-number {
          font-size: 16px;
        }

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }

      .step-info {
        flex: 1;

        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0;
          color: #666;
          line-height: 1.5;

          strong {
            color: #333;
            font-weight: 600;
          }
        }
      }
    }

    // OTP Form Styles
    .otp-form { 
      .otp-field {
        width: 100%;
        margin-bottom: 16px;

        input {
          text-align: center;
          font-size: 18px;
          letter-spacing: 2px;
          font-weight: 600;
        }
      }

      .otp-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;

        button {
          min-width: 160px;

          &.resend-button {
            color: var(--ivy-sky-blue, #2196f3);
            font-size: 14px;

            &:disabled {
              color: #ccc;
            }
          }
        }
      }
    }

    // Password Form Styles
    .password-form { 

      .password-field {
        width: 100%;
        margin-bottom: 16px;
      }

      .password-requirements {
        margin-top: 16px;
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #2196f3;

        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .requirements-list {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 0;
            font-size: 14px;
            color: #666;
            transition: color 0.2s ease;

            &.met {
              color: #4caf50;
            }

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
              color: inherit;
            }
          }
        }
      }
    }

    .password-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 140px;

        &[color="primary"] {
          justify-content: center;
        }
      }
    }

    // Info Box Styles
    .step-info-box {
      padding: 16px;
      background-color: #e3f2fd;
      border-radius: 8px;
      border-left: 4px solid #2196f3;
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .info-icon {
        color: #2196f3;
        font-size: 20px;
        width: 20px;
        height: 20px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .info-content {
        flex: 1;

        p {
          margin: 0 0 8px 0;
          font-size: 14px;
          color: #1565c0;

          strong {
            font-weight: 600;
          }
        }

        ul {
          margin: 0;
          padding-left: 16px;
          color: #0d47a1;

          li {
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .modal-actions {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;

    button {
      min-width: 80px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}

// Responsive Design
@media (max-width: 600px) {
  .reset-password-modal {
    max-width: 100%;
    margin: 0;

    .modal-header {
      padding: 16px;

      h2 {
        font-size: 18px;
      }
    }

    .modal-content {
      padding: 16px;
    }

    .step-container {
      .step-header {
        .step-info {
          h3 {
            font-size: 16px;
          }

          p {
            font-size: 14px;
          }
        }
      }

      .password-actions {
        flex-direction: column;
        align-items: stretch;

        button {
          width: 100%;
          justify-content: center;
        }
      }

      .otp-form {
        .otp-actions {
          align-items: stretch;

          button {
            width: 100%;
          }
        }
      }
    }

    .modal-actions {
      padding: 12px 16px;

      button {
        width: 100%;
      }
    }
  }
}

// Animation for step transitions
.step-container {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
