import { Component, OnInit, OnD<PERSON>roy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { Subject, takeUntil } from 'rxjs';

import { UserService } from '@services/user.service';
import { ToastService } from '@services/toast.service';
import {
  GetUserByIdResponse,
  GetUserByUserCodeResponse,
  UpdateUserRequest,
  EditUserRequest,
  UpdateEmailRequest,
  SendEmailChangeOtpRequest,
  VerifyEmailChangeRequest,
  VerifyEmailChangeNewRequest
} from '@models/user-model/CreateUserModel';
import { PrimaryButton } from '@components/primary-button/primary-button';

@Component({
  selector: 'app-edit-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    PrimaryButton
  ],
  templateUrl: './edit-user.html',
  styleUrls: ['./edit-user.scss']
})
export class EditUser implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Signals
  isLoading = signal<boolean>(false);
  userData = signal<any>(null);
  emailVerificationRequired = signal<boolean>(false);
  emailVerificationCode = signal<string>('');
  
  // Forms
  profileForm!: FormGroup;
  emailForm!: FormGroup;
  verificationForm!: FormGroup;
  
  // Data
  userId!: number;
  userCode!: string;
  originalEmail = '';
  
  // Status options
  statusOptions = [
    { value: 'Active', label: 'Active' },
    { value: 'Inactive', label: 'Inactive' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Deactivated', label: 'Deactivated' },
    { value: 'Locked', label: 'Locked' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private userService: UserService,
    private toastService: ToastService
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.userCode = params['userCode'];
      this.userId = +params['userId'];

      if (this.userCode) {
        // Use userCode if available (preferred method)
        this.loadUserDataByCode();
      } else if (this.userId) {
        // Fallback to userId for backward compatibility
        this.loadUserDataById();
      } else {
        this.toastService.error('Invalid user identifier');
        this.router.navigate(['/admin-user']);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForms(): void {
    this.profileForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      userName: ['', [Validators.required, Validators.minLength(3)]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\+?[\d\s\-\(\)]+$/)]],
      accountStatus: [''] // Made optional since new EditUser API doesn't use it
    });

    this.emailForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });

    this.verificationForm = this.fb.group({
      verificationCode: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6)]]
    });
  }

  /**
   * Load user data by userCode (preferred method)
   */
  private async loadUserDataByCode(): Promise<void> {
    this.isLoading.set(true);

    try {
      const response = await this.userService.getUserByUserCode(this.userCode);
      if (response.isSuccess && response.data) {
        // Map the response data to match the expected format
        const userData = {
          userId: response.data.userId,
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          userName: response.data.userName,
          phoneNumber: response.data.phoneNumber,
          email: response.data.email,
          accountStatus: 'Active', // Default status, you may need to map this based on your business logic
          userCode: response.data.userCode,
          twoFactorEnabled: response.data.twoFactorEnabled
        };

        this.userData.set(userData);
        this.userId = response.data.userId; // Set userId for other operations
        this.originalEmail = response.data.email;
        this.populateForms(userData);
      } else {
        this.toastService.error(response.message || 'Failed to load user data');
        this.router.navigate(['/admin-user']);
      }
    } catch (error) {
      this.toastService.error('Failed to load user data');
      this.router.navigate(['/admin-user']);
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Load user data by userId (fallback method)
   */
  private async loadUserDataById(): Promise<void> {
    this.isLoading.set(true);

    try {
      const response = await this.userService.getUserById(this.userId);
      if (response.isSuccess && response.data) {
        this.userData.set(response.data);
        this.originalEmail = response.data.email;
        this.populateForms(response.data);
      } else {
        this.toastService.error(response.message || 'Failed to load user data');
        this.router.navigate(['/admin-user']);
      }
    } catch (error) {
      this.toastService.error('Failed to load user data');
      this.router.navigate(['/admin-user']);
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Refresh user data using the appropriate method
   */
  private async refreshUserData(): Promise<void> {
    if (this.userCode) {
      await this.loadUserDataByCode();
    } else if (this.userId) {
      await this.loadUserDataById();
    }
  }

  private populateForms(userData: any): void {
    this.profileForm.patchValue({
      firstName: userData.firstName,
      lastName: userData.lastName,
      userName: userData.userName,
      phoneNumber: userData.phoneNumber,
      accountStatus: userData.accountStatus
    });

    this.emailForm.patchValue({
      email: userData.email
    });
  }

  async onSaveProfile() {
    if (this.profileForm.valid) {
      this.isLoading.set(true);

      try {
        let response;

        if (this.userCode) {
          // Use new EditUser API with userCode
          const editRequest: EditUserRequest = {
            userCode: this.userCode,
            firstName: this.profileForm.value.firstName,
            lastName: this.profileForm.value.lastName,
            userName: this.profileForm.value.userName,
            phoneNumber: this.profileForm.value.phoneNumber
          };

          response = await this.userService.editUser(editRequest);
        } else {
          // Fallback to old UpdateUser API with userId
          const updateRequest: UpdateUserRequest = {
            userId: this.userId,
            firstName: this.profileForm.value.firstName,
            lastName: this.profileForm.value.lastName,
            userName: this.profileForm.value.userName,
            phoneNumber: this.profileForm.value.phoneNumber,
            accountStatus: this.profileForm.value.accountStatus
          };

          response = await this.userService.updateUser(updateRequest);
        }

        if (response.isSuccess) {
          this.toastService.success('User profile updated successfully');
          this.refreshUserData(); // Refresh data
        } else {
          this.toastService.error(response.message || 'Failed to update user profile');
        }
      } catch (error) {
        this.toastService.error('Failed to update user profile');
      } finally {
        this.isLoading.set(false);
      }
    } else {
      this.markFormGroupTouched(this.profileForm);
      this.toastService.error('Please fill in all required fields correctly');
    }
  }

  async onSendVerificationCode() {
    if (this.emailForm.valid) {
      const newEmail = this.emailForm.value.email;

      if (newEmail === this.originalEmail) {
        this.toastService.info('Email address is the same as current email');
        return;
      }

      this.isLoading.set(true);

      try {
        let response;

        if (this.userCode) {
          // Use new SendEmailChangeOtp API with userCode
          const sendOtpRequest: SendEmailChangeOtpRequest = {
            userId: this.userCode, // API expects userId but we pass userCode
            newEmail: newEmail
          };

          response = await this.userService.sendEmailChangeOtp(sendOtpRequest);
        } else {
          // Fallback to old UpdateUserEmail API with userId
          const updateEmailRequest: UpdateEmailRequest = {
            userId: this.userId,
            newEmail: newEmail
          };

          response = await this.userService.updateUserEmail(updateEmailRequest);
        }

        if (response.isSuccess) {
          this.emailVerificationRequired.set(true);
          this.toastService.success(`Verification code sent to ${newEmail}`);
        } else {
          this.toastService.error(response.message || 'Failed to send verification code');
        }
      } catch (error) {
        this.toastService.error('Failed to send verification code');
      } finally {
        this.isLoading.set(false);
      }
    } else {
      this.markFormGroupTouched(this.emailForm);
      this.toastService.error('Please enter a valid email address');
    }
  }

  async onVerifyAndUpdateEmail() {
    if (this.verificationForm.valid) {
      this.isLoading.set(true);

      try {
        let response;

        if (this.userCode) {
          // Use new VerifyEmailChange API with userCode
          const verifyNewRequest: VerifyEmailChangeNewRequest = {
            userId: this.userCode, // API expects userId but we pass userCode
            newEmail: this.emailForm.value.email,
            otp: this.verificationForm.value.verificationCode
          };

          response = await this.userService.verifyEmailChangeNew(verifyNewRequest);
        } else {
          // Fallback to old VerifyEmailChange API with userId
          const verifyRequest: VerifyEmailChangeRequest = {
            userId: this.userId,
            email: this.emailForm.value.email,
            verificationCode: this.verificationForm.value.verificationCode
          };

          response = await this.userService.verifyEmailChange(verifyRequest);
        }

        if (response.isSuccess) {
          this.toastService.success('Email updated successfully');
          this.emailVerificationRequired.set(false);
          this.verificationForm.reset();
          this.refreshUserData(); // Refresh data
        } else {
          this.toastService.error(response.message || 'Failed to verify email');
        }
      } catch (error) {
        this.toastService.error('Failed to verify email');
      } finally {
        this.isLoading.set(false);
      }
    } else {
      this.markFormGroupTouched(this.verificationForm);
      this.toastService.error('Please enter a valid 6-digit verification code');
    }
  }

  onResetPassword(): void {
    const confirmToast = this.toastService.confirm(
      'Reset Password',
      `Are you sure you want to reset the password for ${this.userData()?.firstName} ${this.userData()?.lastName}? A temporary password will be generated.`,
      'Reset Password',
      'CONFIRM'
    );

    confirmToast.onAction().subscribe(async () => {
      this.isLoading.set(true);
      
      const response= await this.userService.resetUserPassword(this.userId);
            if (response.isSuccess) {
              this.toastService.success('Password reset successfully. Temporary password has been sent to the user.');
            } else {
              this.toastService.error(response.message || 'Failed to reset password');
            } 
    });
  }
 
  onBackToUsers(): void {
    this.router.navigate(['/admin-user']);
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for template
  getFieldError(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email address';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['pattern']) return `${fieldName} format is invalid`;
    }
    return '';
  }

  getUserDisplayName(): string {
    const user = this.userData();
    return user ? `${user.firstName} ${user.lastName}` : 'User';
  }
}
