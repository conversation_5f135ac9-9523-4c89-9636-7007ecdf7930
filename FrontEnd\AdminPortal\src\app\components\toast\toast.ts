import { Component, inject } from '@angular/core';
import { MatSnackBarRef, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ToastData } from '@models/ToastModel';

@Component({
  selector: 'app-toast',
  imports: [
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: "toast.html",
  styleUrl: './toast.scss'
})
export class Toast {
  private snackBarRef = inject(MatSnackBarRef);
  protected data: ToastData = inject(MAT_SNACK_BAR_DATA);

  protected getIcon(): string {
    switch (this.data.type) {
      case 'success':
        return 'check_circle';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'info';
    }
  }

  protected onAction(): void {
    this.snackBarRef.dismissWithAction();
  }

  protected onClose(): void {
    this.snackBarRef.dismiss();
  }
}
