<div class="mfa-card rounded-2xl bg-ivy-base-white">
  <h2 class="auth-title">MFA Setup</h2>
  <mat-stepper class=" !bg-transparent" orientation="vertical" [linear]="false" #stepper>
    <ng-template matStepperIcon="edit" let-index="index">{{index + 1}}</ng-template>
    <mat-step>
      <ng-template matStepLabel><span class="text-lg"> Install a compatible app on your mobile device or
          computer</span></ng-template>
      <div class="step-content">
        <div class="step-header">
          <mat-icon class="material-symbols step-icon">smartphone</mat-icon>
          <h3>Download an Authenticator App</h3>
        </div>

        <div class="app-recommendations ">
          <p>Choose one of these recommended authenticator apps:</p>

          <div class=" flex gap-4 card   mt-2">
            <div class="app-item">
              <div class="flex flex-col gap-1">
                <strong>Google Authenticator</strong>
                <span>Free • Available on iOS and Android</span>
              </div>
            </div>



            <div class="app-item">
              <div class="flex flex-col gap-1">
                <strong>Microsoft Authenticator</strong>
                <span>Free • Available on iOS and Android</span>
              </div>
            </div>
          </div>

          <p class="text-medium mt-4">
            Download and install one of these apps on your mobile device, then click "Next" to continue.
          </p>
        </div>
      </div>
      <div class="flex justify-end mt-4">
        <button (click)="setupTwoFactor()" matButton matStepperNext
          class="!bg-ivy-sky-blue !text-ivy-base-white !w-[100px]">Next</button>
      </div>
    </mat-step>
    <mat-step>
      <ng-template matStepLabel> <span class="text-lg">Use your virtual MFA app and your device's camara to scanthe QR
          code</span></ng-template>

      <div class="step-content">
        <div class="step-header">
          <mat-icon class=" material-symbols step-icon">qr_code_scanner</mat-icon>
          <h3>Scan QR Code or Enter Secret Key</h3>
        </div>

        @if(isLoading()) {
        <div class="loading-container">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Setting up your authenticator...</p>
        </div>
        } @else {
        <div class="qr-section">
          @if(qrCodeImage()) {
          <div class="qr-code-container">
            <img [src]="qrCodeImage()" alt="QR Code" class="qr-code">
            <p>Scan this QR code with your authenticator app</p>
          </div>

          <div class="divider">
            <span>OR</span>
          </div>

          <div class="secret-key-container">
            <p>Enter this secret key manually:</p>
            <div class="secret-key-display">
              <code>{{ secretKey() }}</code>
              <button mat-icon-button (click)="copySecretKey()" title="Copy to clipboard">
                <mat-icon class="material-symbols">content_copy</mat-icon>
              </button>
            </div>
          </div>
          }
        </div>
        }
      </div>
      <div class="flex justify-end mt-4 gap-2">
        <button matButton matStepperPrevious class=" !w-[100px]">Back</button>
        <button matButton matStepperNext class="!bg-ivy-sky-blue !text-ivy-base-white !w-[100px]">Next</button>
      </div>
    </mat-step>
    <mat-step>
      <ng-template matStepLabel> <span class="text-lg"> Enter the 6-digit code from your authenticator
          app</span></ng-template>

      <div class="step-content">

        <div class="step-header">
          <h3>Verify Your Setup</h3>
        </div>

        <div class="verification-section">
          <p>Enter the 6-digit code from your authenticator app:</p>

          <app-form-input [label]="'Verification Code'" [placeholder]="'Enter 6-digit code'" [type]="'text'"
            [name]="'verificationCode'" [value]="verificationCode()" [required]="true"
            (valueChange)="onVerificationCodeChange($event)">
          </app-form-input>
        </div>
      </div>
      <div class="flex justify-end mt-4 gap-2 ">
        <button matButton matStepperPrevious>Back</button>
        <app-primary-button   [loading]="isLoading()" [text]="'Save'"  (click)="enableMfa()"
          buttonClasses="   !w-[100px !h-[40px]" ></app-primary-button>
      </div>
    </mat-step>
  </mat-stepper>
</div>