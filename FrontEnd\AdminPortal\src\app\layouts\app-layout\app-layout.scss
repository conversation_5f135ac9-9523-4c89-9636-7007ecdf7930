 
.app-sidebar {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    z-index: 1;
}
.mat-app-sidebar{
    box-shadow: 0px 1px 7px 0px var(--ivy-shadow);
    border-top-right-radius:unset;
    border-bottom-right-radius:unset;
    border-right-width: 0px; 
    border-right-style: unset;
}
.app-main {
    height: 100%;
    width: 100%; 
    display: flex;
    flex-direction: column;
}

.app-header {
    width: 100%;
    height: var(--header-height);  
    padding:  11px 24px;
    background-color: var(--ivy-dark-blue);
}

.app-container {
    width: 100%;
    height: calc(100% - var(--header-height));
    overflow-y: auto;  
    padding:  24px;
    background-color: var(--ivy-light-white);

}