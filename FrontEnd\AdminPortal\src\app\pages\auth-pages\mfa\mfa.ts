import { isPlatformBrowser } from '@angular/common';
import { Component, signal, computed, PLATFORM_ID, Inject } from '@angular/core';
import { AuthCard } from '@components/auth-card/auth-card';
import { FormInput } from '@components/form-input/form-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { AuthService } from '@services/auth.service';
import { Router } from '@angular/router';
import { ToastService } from '@services/toast.service';


@Component({
  selector: 'app-mfa',
  imports: [
    AuthCard,
    PrimaryButton,
    FormInput
  ],
  templateUrl: './mfa.html',
  styleUrl: './mfa.scss'
})
export class Mfa {
  mfaCode = signal<string>('');
  errorMessage = signal<string>('');
  isLoading = signal<boolean>(false);


  isValidForm = computed<boolean>(() =>
    !!(this.mfaCode())
  );
  constructor(private toastService: ToastService, private authService: AuthService, private router: Router, @Inject(PLATFORM_ID) private platformId: Object) { }

  onMfaCodeChange(value: string): void {
    this.mfaCode.set(value);
  }
  private redirectToApp(): void {
    let redirectUrl = '/';
    if (isPlatformBrowser(this.platformId)) {
      redirectUrl = localStorage.getItem('invofy_redirect_url') || '/';
      localStorage.removeItem('invofy_redirect_url');
    }
    this.router.navigate([redirectUrl]);
  }
  async onSubmit(): Promise<void> {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      var response = await this.authService.verifyTwoFactor(this.mfaCode());
      if (response && response.isSuccess) {
        this.errorMessage.set('');
        this.toastService.success(response.message||'Login successful! Welcome back.');
        this.redirectToApp();
      }
      else
        this.errorMessage.set(response.message);
      this.isLoading.set(false);


    }

  }

}
