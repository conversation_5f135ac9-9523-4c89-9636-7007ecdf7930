@import "tailwindcss";
*, ::after, ::before, ::backdrop, ::file-selector-button { 
    border: unset;
}
@theme{
        --color-ivy-light-blue: var(--ivy-light-blue);
        --color-ivy-dark-blue: var(--ivy-dark-blue);
        --color-ivy-light-white: var(--ivy-light-white);
        --color-ivy-base-white: var(--ivy-base-white);
        --color-ivy-sky-blue: var(--ivy-sky-blue);
        --color-ivy-orange-blue: var(--ivy-orange-blue);

        --color-ivy-sky-blue-light: var(--ivy-sky-blue-light);
        --color-ivy-sky-blue-dark: var(--ivy-sky-blue-dark);
        --color-ivy-dark-blue-variant: var(--ivy-dark-blue-variant);
        
        --color-ivy-orange-light: var(--ivy-orange-light);
        --color-ivy-orange-dark: var(--ivy-orange-dark);

        --color-ivy-card-background: var(--ivy-card-background);
        --color-ivy-sidebar-background: var(--ivy-sidebar-background);
        --color-ivy-sidebar-active: var(--ivy-sidebar-active);
        --color-ivy-sidebar-hover: var(--ivy-sidebar-hover);
        --color-ivy-header-background: var(--ivy-header-background);
        --color-ivy-border-light: var(--ivy-border-light);
        --color-ivy-text-primary: var(--ivy-text-primary);
        --color-ivy-text-secondary: var(--ivy-text-secondary);
        --color-ivy-text-muted: var(--ivy-text-muted);
        --color-ivy-shadow: var(--ivy-shadow);
        --color-ivy-shadow-dark: var(--ivy-shadow-dark);
        --color-ivy-divider: var(--ivy-divider);
        --color-ivy-divider-dark: var(--ivy-divider-dark);
}