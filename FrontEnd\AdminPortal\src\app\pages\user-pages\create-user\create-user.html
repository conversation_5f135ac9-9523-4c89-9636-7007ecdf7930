
<div class="p-6 max-w-7xl my-0 mx-auto"> 
    <div class="  flex items-center gap-4 mb-6">
    <button mat-icon-button (click)="onBackToUsers()" class="!text-[#666] !hover:bg-[#0000000a]">
        <mat-icon class="material-symbols">arrow_back</mat-icon>
    </button>
    <h1 calss="m-0 text-2xl font-bold color-[#333]">Create New User</h1>
    </div>

    <div class="content-layout"> 
    <div class="progress-sidebar">
        <app-step-progress [steps]="steps" [currentStep]="currentStep()"></app-step-progress>
    </div> 
    <div class="main-content ">
        <mat-card class="form-card !bg-ivy-base-white"> 
        @if (currentStep() === 0) {
            <mat-card-header>
            <mat-card-title>Create New User</mat-card-title>
            <mat-card-subtitle>Add a new user to the system. They will receive an OTP for email verification.</mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
            <div class="user-form">
                <div class="form-row">
                <app-form-input
                    label="First Name"
                    [value]="firstName()"
                    [required]="true"
                    placeholder="Enter first name"
                    (valueChange)="onFirstNameChange($event)"
                    class="form-field">
                </app-form-input>

                <app-form-input
                    label="Last Name"
                    [value]="lastName()"
                    [required]="true"
                    placeholder="Enter last name"
                    (valueChange)="onLastNameChange($event)"
                    class="form-field">
                </app-form-input>
                </div> 
                <div class="form-row">
                <app-form-input
                    label="User Name"
                    [value]="userName()"
                    [required]="true"
                    placeholder="Enter username"
                    (valueChange)="onUserNameChange($event)"
                    class="form-field full-width">
                </app-form-input>
                </div> 
                <div class="form-row">
                <app-form-input
                    label="Email"
                    type="email"
                    [value]="email()"
                    [required]="true"
                    placeholder="Enter email address"
                    (valueChange)="onEmailChange($event)"
                    class="form-field">
                </app-form-input>

                <app-form-input
                    label="Confirm Email"
                    type="email"
                    [value]="confirmEmail()"
                    [required]="true"
                    placeholder="Confirm email address"
                    (valueChange)="onConfirmEmailChange($event)"
                    class="form-field">
                </app-form-input>
                </div>

                @if (emailMismatch) {
                <div class="error-message">
                    <mat-icon class="material-symbols">error</mat-icon>
                    <span>Email addresses do not match</span>
                </div>
                } 
                <div class="form-row">
                <app-password-input
                    label="Password"
                    [value]="password()"
                    [required]="true"
                    placeholder="Enter password"
                    (valueChange)="onPasswordChange($event)"
                    class="form-field">
                </app-password-input>

                <app-password-input
                    label="Confirm"
                    [value]="confirmPassword()"
                    [required]="true"
                    placeholder="Confirm password"
                    (valueChange)="onConfirmPasswordChange($event)"
                    class="form-field">
                </app-password-input>
                </div>

                @if (passwordMismatch) {
                <div class="error-message">
                    <mat-icon class="material-symbols">error</mat-icon>
                    <span>Passwords do not match</span>
                </div>
                } 
                <div class="form-row">
                <app-form-input
                    label="Phone Number"
                    type="tel"
                    [value]="phoneNumber()"
                    [required]="true"
                    placeholder="Enter phone number"
                    (valueChange)="onPhoneNumberChange($event)"
                    class="form-field full-width">
                </app-form-input>
                </div>
            </div>
            </mat-card-content>

            <mat-card-actions class="form-actions"> 
            <app-primary-button
                text="Next: Verify Email"
                [loading]="isLoading()"
                [disabled]="!isUserFormValid"
                (click)="onNextStep()">
            </app-primary-button>
            </mat-card-actions>
        } 
        @if (currentStep() === 1) {
            <mat-card-header class="verification-header">
            <div class="verification-icon">
                <mat-icon class="material-symbols">email</mat-icon>
            </div>
            <mat-card-title>Email Verification</mat-card-title>
            <mat-card-subtitle>Verify Email Address</mat-card-subtitle>
            </mat-card-header>

            <mat-card-content class="verification-content">
            <p class="verification-text">
                We've sent a 6-digit verification code to 
                <strong>{{ createdUserEmail() }}</strong><br>
                Please enter the code below to activate the account.
            </p>

            <div class="verification-form">
                <app-form-input
                label="Verification Code"
                [value]="verificationCode()"
                [required]="true"
                placeholder="Enter 6-digit code"
                (valueChange)="onVerificationCodeChange($event)"
                class="verification-input">
                </app-form-input>

                <button mat-button 
                        type="button" 
                        (click)="onResendCode()" 
                        [disabled]="isLoading()"
                        class="resend-button">
                Resend Code
                </button>
            </div>
            </mat-card-content>

            <mat-card-actions class="form-actions">
            <button mat-button (click)="onCancel()" [disabled]="isLoading()">
                Verify Later
            </button>
            <app-primary-button
                text="Verify Email"
                [loading]="isLoading()"
                [disabled]="!isVerificationFormValid"
                (click)="onNextStep()">
            </app-primary-button>
            </mat-card-actions>
        } 
        </mat-card>
    </div>
    </div>
</div>