<app-auth-card 
  [title]="'Login'"
  [subtitle]="'Please enter your details'"
  class="max-w-full  "
  > 
  <form class="flex flex-col gap-4 " >
    @if (errorMessage()) {
      <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
        {{ errorMessage() }}
      </div>
    }
    <app-form-input
      [label]="'Username'"
      [placeholder]="'<EMAIL>'"
      [type]="'text'"
      [name]="'userName'"
      [iconName]="'person'"
      [value]="userName()"
      [required]="true"
      [autocomplete]="'username'"
      (valueChange)="onUserNameChange($event)">
    </app-form-input>
    <app-password-input
      [label]="'Password'"
      [placeholder]="'Enter your password'"
      [name]="'password'"
      [value]="password()"
      [required]="true"
      [autocomplete]="'current-password'"
      (valueChange)="onPasswordChange($event)">
    </app-password-input>
    <app-primary-button
      [text]="'Login'"
      [type]="'button'"
      [loading]="isLoading()"
      [disabled]="!isValidForm()"
      (buttonClick)="onLogin()"
      >
    </app-primary-button>
    <div class="text-center mt-4">
      <a href="#" class="text-sm font-medium text-ivy-sky-blue no-underline hover:text-ivy-sky-blue-dark hover:underline" (click)="onForgotPassword($event)">
        Forgot Password?
      </a>
    </div>
  </form>
</app-auth-card>  