import { Component, input, output, signal, effect } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-password-input',
  templateUrl: './password-input.html',
  styleUrl: './password-input.scss',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    FormsModule
  ]
})
export class PasswordInput {
  // Input signals
  label = input<string>('Password');
  placeholder = input<string>('Enter your password');
  name = input<string>('password');
  required = input<boolean>(false);
  value = input<string>('');
  disabled = input<boolean>(false);
  autocomplete = input<string>('current-password');

  // Output signals
  valueChange = output<string>();

  // Internal signals
  showPassword = signal<boolean>(false);
  currentValue = signal<string>('');

  constructor() {
    // Effect to sync currentValue with input value changes
    effect(() => {
      this.currentValue.set(this.value());
    });
  }

  togglePassword(): void {
    this.showPassword.update(show => !show);
  }

  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const newValue = inputElement.value;
    this.currentValue.set(newValue);
    this.valueChange.emit(newValue);
  }
}
