import { Injectable, signal } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { GetUsersApiResponse, UserQueryParams } from '@models/UserModel';
import {
  CreateUserRequest,
  CreateUserResponse,
  EmailVerificationRequest,
  EmailVerificationResponse,
  ResendCodeRequest,
  ResendCodeResponse,
  DeleteUserRequest,
  DeleteUserResponse,
  GetUserByIdResponse,
  UpdateUserRequest,
  UpdateUserResponse,
  UpdateEmailRequest,
  UpdateEmailResponse,
  VerifyEmailChangeRequest,
  VerifyEmailChangeResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  UpdateMfaStatusRequest,
  UpdateMfaStatusResponse,
  MarkAsInactiveRequest,
  MarkAsInactiveResponse,
  GetUserByUserCodeRequest,
  GetUserByUserCodeResponse,
  EditUserRequest,
  SendEmailChangeOtpRequest,
  SendEmailChangeOtpResponse,
  VerifyEmailChangeNewRequest,
  VerifyEmailChangeNewResponse
} from '@models/user-model/CreateUserModel';
import { BaseService } from './base.service';
import { ApiBaseResponse } from '@models/ApiResponse';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private _isLoading = signal<boolean>(false);

  // Public computed signals
  isLoading = this._isLoading.asReadonly();

  constructor(
    private baseService: BaseService
  ) { }

  /**
   * Get all users with pagination and filtering
   */
  async getAllUsers(params: UserQueryParams = {}): Promise<ApiBaseResponse<GetUsersApiResponse>> {
    this._isLoading.set(true);

    // Set default parameters
    const defaultParams: UserQueryParams = {
      PageIndex: 1,
      PageSize: 10,
      SearchText: '',
      SortColumn: '',
      SortOrder: 'ASC',
      IsPaginated: true,
      ...params
    };

    // Build HTTP params
    let httpParams = new HttpParams();
    Object.entries(defaultParams).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return await this.baseService.get<GetUsersApiResponse>('User/GetAllUsers', {
      params: httpParams
    });
  }


  /**
   * Create a new user
   */
  async createUser(createUserRequest: CreateUserRequest): Promise<ApiBaseResponse<CreateUserResponse>> {
    this._isLoading.set(true);

    return await this.baseService.post<CreateUserResponse>('User/CreateNewUser', createUserRequest, {
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/json'
      }
    });
  }

  async verifyUserEmail(verificationRequest: EmailVerificationRequest): Promise<ApiBaseResponse<EmailVerificationResponse>> {
    this._isLoading.set(true);

    return await this.baseService.post<EmailVerificationResponse>('User/VerifyEmail', verificationRequest);
  }

  async resendVerificationCode(resendRequest: ResendCodeRequest): Promise<ApiBaseResponse<ResendCodeResponse>> {
    this._isLoading.set(true);

    return await this.baseService.post<ResendCodeResponse>('User/ResendVerificationCode', resendRequest);
  }

  async getUserById(userId: number): Promise<ApiBaseResponse<GetUserByIdResponse>> {
    this._isLoading.set(true);

    return await this.baseService.get<GetUserByIdResponse>(`User/GetUserById/${userId}`);
  }

  /**
   * Get user by userCode (GUID)
   */
  async getUserByUserCode(userCode: string): Promise<ApiBaseResponse<GetUserByUserCodeResponse>> {
    this._isLoading.set(true);

    try {
      return await this.baseService.get<GetUserByUserCodeResponse>(`User/GetUserByUserCode/${userCode}`);  
    } finally {
      this._isLoading.set(false);
    }
  }


  async updateUser(updateRequest: UpdateUserRequest): Promise<ApiBaseResponse<UpdateUserResponse>> {
    this._isLoading.set(true);

    return this.baseService.put<UpdateUserResponse>('User/UpdateUser', updateRequest);
  }

  /**
   * Edit user using userCode (new API)
   */
  async editUser(editRequest: EditUserRequest): Promise<ApiBaseResponse> {
    this._isLoading.set(true);

    try {
      return await this.baseService.post<any>('User/EditUser', editRequest);
    } finally {
      this._isLoading.set(false);
    }
  }

  async updateUserEmail(updateEmailRequest: UpdateEmailRequest): Promise<ApiBaseResponse<UpdateEmailResponse>> {
    this._isLoading.set(true);

    return this.baseService.put<UpdateEmailResponse>('User/UpdateEmail', updateEmailRequest);
  }

  /**
   * Send email change OTP using userCode (new API)
   */
  async sendEmailChangeOtp(request: SendEmailChangeOtpRequest): Promise<ApiBaseResponse<SendEmailChangeOtpResponse>> {
    this._isLoading.set(true);

    try {
      return await this.baseService.post<SendEmailChangeOtpResponse>('User/SendEmailChangeOtp', request);
    } finally {
      this._isLoading.set(false);
    }
  }

  async  verifyEmailChange(verifyRequest: VerifyEmailChangeRequest): Promise<ApiBaseResponse<VerifyEmailChangeResponse>> {
    this._isLoading.set(true);

    return await this.baseService.post<VerifyEmailChangeResponse>('User/VerifyEmailChange', verifyRequest);
  }

  /**
   * Verify email change using userCode (new API)
   */
  async verifyEmailChangeNew(request: VerifyEmailChangeNewRequest): Promise<ApiBaseResponse<VerifyEmailChangeNewResponse>> {
    this._isLoading.set(true);

    try {
      return await this.baseService.post<VerifyEmailChangeNewResponse>('User/VerifyEmailChange', request);
    } finally {
      this._isLoading.set(false);
    }
  }

  async resetUserPassword(userId: number): Promise<ApiBaseResponse<ResetPasswordResponse>> {
    this._isLoading.set(true);

    const resetRequest: ResetPasswordRequest = {
      userId: userId
    };

    return this.baseService.post<ResetPasswordResponse>('User/ResetPassword', resetRequest);
  }

async  updateMfaStatus(updateMfaRequest: UpdateMfaStatusRequest): Promise<ApiBaseResponse<UpdateMfaStatusResponse>> {
    this._isLoading.set(true); 
    return await this.baseService.post<UpdateMfaStatusResponse>('User/UpdateMfaStatus', updateMfaRequest, {
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/json'
      }
    });
  }

  async deleteUser(userId: number): Promise<ApiBaseResponse<DeleteUserResponse>> {
    this._isLoading.set(true);

    const deleteRequest: DeleteUserRequest = {
      userId: userId
    };

    return await this.baseService.deleteWithBody<DeleteUserResponse>('User/DeleteUser', deleteRequest, {
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Mark user as active or inactive
   */
  async markAsInactive(userCode: string, isActive: boolean): Promise<ApiBaseResponse<MarkAsInactiveResponse>> {
    this._isLoading.set(true);

    const markAsInactiveRequest: MarkAsInactiveRequest = {
      userCode: userCode,
      isActive: isActive
    };

    try {
      const response = await this.baseService.post<MarkAsInactiveResponse>('User/MarkAsInavtive', markAsInactiveRequest, {
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/json'
        }
      });
      return response;
    } finally {
      this._isLoading.set(false);
    }
  }

  /**
   * Activate user (mark as active)
   */
  async activateUser(userCode: string): Promise<ApiBaseResponse<MarkAsInactiveResponse>> {
    return this.markAsInactive(userCode, true);
  }

  /**
   * Deactivate user (mark as inactive)
   */
  async deactivateUser(userCode: string): Promise<ApiBaseResponse<MarkAsInactiveResponse>> {
    return this.markAsInactive(userCode, false);
  }

}


