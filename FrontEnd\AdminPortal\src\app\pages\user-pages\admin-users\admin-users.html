<div class="page-container">
    <!-- Header Section -->
    <div class="header-section">
        <div class="header-left">
            <h2 class="page-count">Admin Users({{ totalRecords() }})</h2>
        </div>
        <div class="header-right">
            <app-table-search-input buttonClasses="!h-14 rounded-full" iconName="search" appearance="outline"
                placeholder="Search User" [value]="searchText()" (input)="onSearchChange($event)"
                class="search-input !h-14 rounded-full" />

            <button mat-raised-button color="primary" class="add-btn !text-ivy-base-white !bg-ivy-dark-blue !h-14"
                (click)="onAddUser()">
                <mat-icon class="material-symbols">add</mat-icon>
                Add User
            </button>
        </div>
    </div>

    <!-- Error Message -->
    @if(errorMessage()) {
    <div class="error-message">
        <mat-icon class="material-symbols">error</mat-icon>
        <span>{{ errorMessage() }}</span>
    </div>
    }

    <!-- Loading Spinner -->
    @if(isLoading()) {
    <div class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading users...</p>
    </div>
    }

    <!-- Users Table -->
    @if(!isLoading()) {
    <div class="table-container">
        <table mat-table [dataSource]="dataSource" class="data-table">

            <!-- Name Column -->
            <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let user">{{ user.name }}</td>
            </ng-container>

            <!-- Email Column -->
            <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let user">{{ user.email }}</td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>

                <td mat-cell *matCellDef="let user">
                    <span [class]="getStatusClass(user.status)">{{user.status  }}</span>
                </td>
            </ng-container>

            <!-- MFA Column -->
            <ng-container matColumnDef="mfa">
                <th mat-header-cell *matHeaderCellDef>MFA</th>


                <td mat-cell *matCellDef="let user">
                    <div class="mfa-status">
                        <mat-icon class="material-symbols mfa-icon !text-[24px]" [class]="getMfaIconClass(user.mfa)">
                            {{ user.mfa.toLowerCase() === 'enabled' ? 'check_circle' : 'block' }}
                        </mat-icon>
                        <span>{{ user.mfa }}</span>
                    </div>
                </td>
            </ng-container>

            <!-- Created Date Column -->
            <ng-container matColumnDef="createdDate">
                <th mat-header-cell *matHeaderCellDef>Created Date</th>
                <td mat-cell *matCellDef="let user">{{ formatDate(user.createdDate) }}</td>
            </ng-container>

            <!-- Last Login Column -->
            <ng-container matColumnDef="lastLogin">
                <th mat-header-cell *matHeaderCellDef>Last Login</th>
                <td mat-cell *matCellDef="let user">{{ formatDate(user.lastLogin) }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let user">
                    <button mat-icon-button [matMenuTriggerFor]="actionsMenu"
                            [matMenuTriggerData]="{user: user}"
                            class="actions-button">
                        <mat-icon class="material-symbols">more_vert</mat-icon>
                    </button>

                    <!-- Actions Menu Template -->
                    <mat-menu #actionsMenu="matMenu" class="user-actions-menu">
                        <ng-template matMenuContent let-user="user">
                            <!-- Edit Action -->
                            <button mat-menu-item (click)="onEditUser(user)" class="menu-item-edit">
                                <mat-icon class="material-symbols menu-icon">edit</mat-icon>
                                <span>Edit</span>
                            </button>

                            <!-- Reset Password Action -->
                            <button mat-menu-item (click)="onResetPassword(user)"  >
                                <mat-icon class="material-symbols menu-icon">key</mat-icon>
                                <span>Reset Password</span>
                            </button>

                            <!-- MFA Actions -->
                            @if (user.mfa.toLowerCase() === 'enabled') {
                                <button mat-menu-item (click)="onDisableMfa(user)"  >
                                    <mat-icon class="material-symbols menu-icon">block</mat-icon>
                                    <span>Disable MFA</span>
                                </button>
                            }

                            <!-- Status Actions -->
                            @if (user.status?.toLowerCase() === 'active') {
                                <button mat-menu-item (click)="onDeactivateUser(user)" >
                                    <mat-icon class="material-symbols menu-icon">person_off</mat-icon>
                                    <span>Inactive</span>
                                </button>
                            } @else {
                                <button mat-menu-item (click)="onActivateUser(user)"  >
                                    <mat-icon class="material-symbols menu-icon">person</mat-icon>
                                    <span>Activate</span>
                                </button>
                            }

                            <mat-divider></mat-divider>

                            <!-- Delete Action -->
                            <button mat-menu-item (click)="onDeleteUser(user)"  >
                                <mat-icon class="material-symbols menu-icon">delete</mat-icon>
                                <span>Delete</span>
                            </button>
                        </ng-template>
                    </mat-menu>
                </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        @if(dataSource.data.length === 0 && !isLoading()) {
        <div class="no-data-container">
            <mat-icon class="material-symbols no-data-icon">people_outline</mat-icon>
            <h3>No users found</h3>
            <p>{{ searchText() ? 'No users match your search criteria.' : 'No users have been created yet.' }}</p>
        </div>
        }
    </div>

    @if(totalRecords() > 0) {
    <mat-paginator [length]="totalRecords()" [pageSize]="pageSize()" [pageIndex]="pageIndex()"
        [pageSizeOptions]="[5, 10, 25, 50]" (page)="onPageChange($event)" showFirstLastButtons
        aria-label="Select page of users">
    </mat-paginator>
    }
    }
</div>