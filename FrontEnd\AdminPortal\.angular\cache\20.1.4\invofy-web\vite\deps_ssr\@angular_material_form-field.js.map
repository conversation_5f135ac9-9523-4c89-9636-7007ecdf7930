{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/form-field.mjs"], "sourcesContent": ["export { a as MAT_ERROR, h as MAT_FORM_FIELD, i as MAT_FORM_FIELD_DEFAULT_OPTIONS, d as MAT_PREFIX, f as MAT_SUFFIX, b as <PERSON><PERSON><PERSON><PERSON>, j as MatFormField, k as MatFormFieldControl, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON>fix, g as MatSuffix, m as getMatFormFieldDuplicatedHintError, n as getMatFormFieldMissingControlError, l as getMatFormFieldPlaceholderConflictError } from './form-field-D9B5IUZf.mjs';\nexport { M as MatFormFieldModule } from './module-Dj5gfeAg.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport '@angular/core';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-ChQ1vjiF.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\nimport './common-module-cKSwHniA.mjs';\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matFormFieldAnimations = {\n    // Represents:\n    // trigger('transitionMessages', [\n    //   // TODO(mmalerba): Use angular animations for label animation as well.\n    //   state('enter', style({opacity: 1, transform: 'translateY(0%)'})),\n    //   transition('void => enter', [\n    //     style({opacity: 0, transform: 'translateY(-5px)'}),\n    //     animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    //   ]),\n    // ])\n    /** Animation that transitions the form field's error and hint messages. */\n    transitionMessages: {\n        type: 7,\n        name: 'transitionMessages',\n        definitions: [\n            {\n                type: 0,\n                name: 'enter',\n                styles: {\n                    type: 6,\n                    styles: { opacity: 1, transform: 'translateY(0%)' },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: 'void => enter',\n                animation: [\n                    { type: 6, styles: { opacity: 0, transform: 'translateY(-5px)' }, offset: null },\n                    { type: 4, styles: null, timings: '300ms cubic-bezier(0.55, 0, 0.55, 0.2)' },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { matFormFieldAnimations };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,kBAAO;AACP,uBAAO;AAaP,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW3B,oBAAoB;AAAA,IAChB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACT;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ,EAAE,SAAS,GAAG,WAAW,iBAAiB;AAAA,UAClD,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,UACP,EAAE,MAAM,GAAG,QAAQ,EAAE,SAAS,GAAG,WAAW,mBAAmB,GAAG,QAAQ,KAAK;AAAA,UAC/E,EAAE,MAAM,GAAG,QAAQ,MAAM,SAAS,yCAAyC;AAAA,QAC/E;AAAA,QACA,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,IACA,SAAS,CAAC;AAAA,EACd;AACJ;", "names": []}