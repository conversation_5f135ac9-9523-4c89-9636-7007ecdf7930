import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { User } from '@models/UserModel';

export interface UserActionsData {
  user: User;
}

export interface UserActionResult {
  action: 'mark-inactive' | 'delete-permanently' | 'cancel';
  user: User;
}

@Component({
  selector: 'app-user-actions-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl:'user-actions-modal.html',
  styleUrls: ['./user-actions-modal.scss']
})
export class UserActionsModal {
  constructor(
    public dialogRef: MatDialogRef<UserActionsModal>,
    @Inject(MAT_DIALOG_DATA) public data: UserActionsData
  ) {}

  onCancel(): void {
    this.dialogRef.close({ action: 'cancel', user: this.data.user });
  }

  onMarkInactive(): void {
    this.dialogRef.close({ action: 'mark-inactive', user: this.data.user });
  }

  onDeletePermanently(): void {
    this.dialogRef.close({ action: 'delete-permanently', user: this.data.user });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'status-active';
      case 'inactive':
        return 'status-inactive';
      case 'pending':
        return 'status-pending';
      case 'deactivated':
        return 'status-deactivated';
      case 'locked':
        return 'status-locked';
      default:
        return 'status-default';
    }
  }
}
