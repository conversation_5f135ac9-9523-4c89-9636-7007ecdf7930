import { Component, input, output, signal, effect } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-form-input',
  templateUrl: './form-input.html',
  styleUrl: './form-input.scss',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    FormsModule
  ]
})
export class FormInput {
  // Input signals
  label = input<string>('');
  placeholder = input<string>('');
  type = input<string>('text');
  name = input<string>('');
  required = input<boolean>(false);
  value = input<string>('');
  iconName = input<string>('');
  disabled = input<boolean>(false);
  autocomplete = input<string>('on');

  // Output signals
  valueChange = output<string>();

  // Internal signal for managing input value
  currentValue = signal<string>('');

  constructor() {
    // Effect to sync currentValue with input value changes
    effect(() => {
      this.currentValue.set(this.value());
    });
  }

  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const newValue = inputElement.value;
    this.currentValue.set(newValue);
    this.valueChange.emit(newValue);
  }
}
