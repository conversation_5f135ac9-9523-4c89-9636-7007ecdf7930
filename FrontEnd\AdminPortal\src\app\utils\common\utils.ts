
import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common'; 

@Injectable({
    providedIn: 'root'
})
export class Utils { 

    constructor( @Inject(PLATFORM_ID) private platformId: Object ) { }
    isBrowser(): boolean {
        return isPlatformBrowser(this.platformId);
    }
    isTokenValid(token:string | null): boolean { 
    if (!token) return false;

    try { 
        const parts = token.split('.');
        if (parts.length !== 3) return false;

        const payload = JSON.parse(atob(parts[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        return payload.exp > currentTime;
        } catch (error) {
        return false;
    }
    }
}

