import { Component,computed,signal } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Header } from '@components/header/header';
import { Sidebar } from '@components/sidebar/sidebar';
import {MatSidenavModule} from '@angular/material/sidenav';

@Component({
  selector: 'app-app-layout',
  imports: [MatSidenavModule,RouterModule, Sidebar, Header],
  templateUrl: './app-layout.html',
  styleUrl: './app-layout.scss'
})
export class AppLayout {
  sidebarCollapsed=signal(true);
  sidebarWidth = computed(() => !this.sidebarCollapsed() ? 'var(--sidebar-collapsed-width)' : 'var(--sidebar-width)');

  toggleSidebar(): void { 
    this.sidebarCollapsed.set(!this.sidebarCollapsed()); 
  }
}
