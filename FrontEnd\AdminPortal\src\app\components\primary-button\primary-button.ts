import { Component, input, output, computed } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-primary-button',
  templateUrl: './primary-button.html',
  styleUrl: './primary-button.scss',
  standalone: true,
  imports: [
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatIconModule
  ]
})
export class PrimaryButton {
  // Input signals
  text = input<string>('submit');
  type = input<string>('button');
  disabled = input<boolean>(false);
  loading = input<boolean>(false);
  size = input<'small' | 'medium' | 'large'>('medium');
  buttonClasses = input<string>('');

  // Output signals
  buttonClick = output<Event>();

  // Computed signal for button state
  isDisabled = computed(() => this.disabled() || this.loading());

  // Computed signal for CSS classes
  cssClasses = computed(() => {
    return `primary-button ${this.buttonClasses()} ${this.size()}`;
  });

  onClick(event: Event): void { 
    if (!this.isDisabled() && this.buttonClick) {
      this.buttonClick.emit(event);
    }
  }
}

