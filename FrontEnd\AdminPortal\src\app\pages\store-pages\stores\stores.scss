.element-row {
  cursor: pointer;
  
  &:hover {
    background-color: rgba(0, 123, 255, 0.05);
  }
}

.detail-row {
  height: auto;
}
.detail-row td{
  padding: 0 !important;
}

// Store name cell with expand icon
.store-name-cell {
  cursor: pointer;

  .store-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      color: #666;
      transition: transform 0.3s ease, color 0.2s ease;
      flex-shrink: 0;

      &.rotated {
        transform: rotate(90deg);
      }

      &:hover {
        color: #007bff;
      }
    }

    .store-name {
      font-weight: 500;
      color: #333;
      transition: color 0.2s ease;

      &:hover {
        color: #007bff;
      }
    }
  }

}

.expanded-content {
  max-height: 0;
  overflow: hidden;
  padding: 0 16px;
  background: #f5f5f5;
  transition: max-height 0.3s ease, padding 0.3s ease;

  &.show {
    max-height: 500px; // should be bigger than expected content
    padding: 16px;
  }
}
.page-container {

    // Header Section
    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .header-left {
            .page-count {
                font-size: 24px;
                font-weight: 600;
                color: #333;
                margin: 0;
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;

            .search-input {
                width: 300px;

                .mat-mdc-form-field-subscript-wrapper {
                    display: none;
                }
            }
        }
    }

    // Error Message
    .error-message {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
        margin-bottom: 16px;

        mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
        }
    }

    // Loading Container
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px;
        color: #666;

        p {
            margin-top: 16px;
            font-size: 16px;
        }
    }

    // Table Container
    .table-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .data-table {
            width: 100%;

            // Header styling
            .mat-mdc-header-row {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;

                .mat-mdc-header-cell {
                    font-weight: 600;
                    color: #495057;
                    font-size: 14px;
                    padding: 16px 12px;
                    border-bottom: none;
                }
            }

            // Row styling
            .mat-mdc-row {
                border-bottom: 1px solid #dee2e6;

                &:hover {
                    background-color: #f8f9fa;
                }

                .mat-mdc-cell {
                    padding: 16px 12px;
                    font-size: 14px;
                    color: #495057;
                    border-bottom: none;
                }
            }

            // Store name cell with expanded info
            .store-name-cell {
                .store-info {
                    .store-name {
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 4px;
                    }

                    .store-details {
                        display: flex;
                        gap: 12px;
                        margin-bottom: 2px;

                        .detail-item {
                            font-size: 12px;
                            color: #666;
                            background-color: #f8f9fa;
                            padding: 2px 6px;
                            border-radius: 3px;
                        }
                    }

                    .store-stats {
                        display: flex;
                        gap: 12px;

                        .stat-item {
                            font-size: 12px;
                            color: #007bff;
                            font-weight: 500;
                        }
                    }
                }
            }
        }

        // No data message
        .no-data-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 48px;
            text-align: center;
            color: #666;

            .no-data-icon {
                font-size: 48px;
                width: 48px;
                height: 48px;
                margin-bottom: 16px;
                color: #ccc;
            }

            h3 {
                margin: 0 0 8px 0;
                font-size: 18px;
                font-weight: 500;
            }

            p {
                margin: 0;
                color: #999;
            }
        }

        // Progress Status styling
        .status-completed {
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-in-progress {
            background-color: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-cancelled {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
    }

    // Angular Material Paginator styling
    mat-paginator {
        background-color: white;
        border-top: 1px solid #dee2e6;
    }
}

// Responsive design
@media (max-width: 768px) {
    .page-container {
        padding: 16px;

        .header-section {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;

            .header-right {
                justify-content: space-between;

                .search-input {
                    flex: 1;
                    margin-right: 16px;
                }
            }
        }

        .table-container {
            .data-table {
                .store-name-cell {
                    .store-info {
                        .store-details,
                        .store-stats {
                            flex-direction: column;
                            gap: 4px;
                        }
                    }
                }
            }
        }
    }
}
