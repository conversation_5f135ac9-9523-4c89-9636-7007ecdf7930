import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { UserActionsModal, UserActionsData } from './user-actions-modal';
import { User } from '@models/UserModel';

describe('UserActionsModal', () => {
  let component: UserActionsModal;
  let fixture: ComponentFixture<UserActionsModal>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<UserActionsModal>>;

  const mockUser: User = {
    userId: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'Active',
    mfa: 'Enabled',
    createdDate: '2024-01-01',
    lastLogin: '2024-01-15'
  };

  const mockData: UserActionsData = {
    user: mockUser
  };

  beforeEach(async () => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        UserActionsModal,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockData }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UserActionsModal);
    component = fixture.componentInstance;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<UserActionsModal>>;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display user information', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('h3').textContent).toContain('John Doe');
    expect(compiled.querySelector('.user-email').textContent).toContain('<EMAIL>');
    expect(compiled.querySelector('.user-status').textContent).toContain('Active');
  });

  it('should close dialog with cancel action when cancel is clicked', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith({
      action: 'cancel',
      user: mockUser
    });
  });

  it('should close dialog with mark-inactive action when mark inactive is clicked', () => {
    component.onMarkInactive();
    expect(mockDialogRef.close).toHaveBeenCalledWith({
      action: 'mark-inactive',
      user: mockUser
    });
  });

  it('should close dialog with delete-permanently action when delete permanently is clicked', () => {
    component.onDeletePermanently();
    expect(mockDialogRef.close).toHaveBeenCalledWith({
      action: 'delete-permanently',
      user: mockUser
    });
  });

  it('should return correct status class', () => {
    expect(component.getStatusClass('active')).toBe('status-active');
    expect(component.getStatusClass('inactive')).toBe('status-inactive');
    expect(component.getStatusClass('pending')).toBe('status-pending');
    expect(component.getStatusClass('unknown')).toBe('status-default');
  });
});
