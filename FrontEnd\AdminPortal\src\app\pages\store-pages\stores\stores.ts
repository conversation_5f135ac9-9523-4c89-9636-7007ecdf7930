import { AfterViewInit, Component, ViewChild, signal, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

import { Store, StoreQueryParams } from '@models/StoreModel';
import { StoreService } from '@services/store.service';
import { ToastService } from '@services/toast.service';
import { TableSearchInput } from '@components/table-components/table-search-input/table-search-input';

@Component({
  selector: 'app-stores',
  imports: [
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatSelectModule,
    MatDividerModule,
    FormsModule,
    TableSearchInput
  ],
  templateUrl: './stores.html',
  styleUrl: './stores.scss'
})
export class Stores implements OnInit, AfterViewInit, OnDestroy {
  displayedColumns: string[] = ['storeName', 'owner', 'email', 'phone', 'location', 'progressStatus', 'createdDate', 'actions'];
  dataSource = new MatTableDataSource<Store>([]);
  @ViewChild(MatPaginator, { static: false }) paginator?: MatPaginator;

  isLoading = signal<boolean>(false);
  errorMessage = signal<string>('');
  searchText = signal<string>('');
  sortColumn = signal<string>('');
  sortOrder = signal<string>('');
  totalRecords = signal<number>(0);

  pageSize = signal<number>(10);
  pageIndex = signal<number>(0);
  pageSizeOptions = [5, 10, 25, 50];

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private storeService: StoreService,
    private toastService: ToastService,
    private router: Router,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.setupSearch();
  }

  expandedElement: Store[]  = [];

  toggleRow(row: Store): void {
    this.expandedElement = this.expandedElement.includes(row) ? this.expandedElement.filter(r => r !== row) : [...this.expandedElement, row];
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.loadStores();
    }, 0);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchText => {
      this.searchText.set(searchText);
      this.pageIndex.set(0);
      this.loadStores();
    });
  }

  async loadStores() {
    this.isLoading.set(true);
    this.errorMessage.set('');

    const params: StoreQueryParams = {
      pageIndex: this.pageIndex() + 1,
      pageSize: this.pageSize(),
      searchText: this.searchText() || undefined,
      sortColumn: this.sortColumn() || undefined,
      sortOrder: this.sortOrder() || undefined,
      isPaginated: true
    };

    try {
      const response = await this.storeService.getAllStores(params);
      if (response?.isSuccess) {
        this.dataSource.data = response.data?.stores || [];
        this.totalRecords.set(response.data?.pagination?.totalRecords || 0);
      } else {
        this.errorMessage.set(response?.message || 'Failed to load stores');
        this.dataSource.data = [];
        this.totalRecords.set(0);
      }
    } catch (error) { 
      this.errorMessage.set('An error occurred while loading stores');
      this.dataSource.data = [];
      this.totalRecords.set(0);
    }
    
    this.isLoading.set(false);
  }

  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchSubject.next(target.value);
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex.set(event.pageIndex);
    this.pageSize.set(event.pageSize);
    this.loadStores();
  }

  onRefresh(): void {
    this.loadStores();
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  }

  onEditStore(store: Store): void {
    // Navigate to edit store page
    this.router.navigate(['/store/edit'], {
      queryParams: {
        storeCode: store.storeMasterCode,
        storeId: store.storeMasterId
      }
    });
  }

  onViewStore(store: Store): void {
    // Navigate to store details page
    this.router.navigate(['/store/details'], {
      queryParams: {
        storeCode: store.storeMasterCode,
        storeId: store.storeMasterId
      }
    });
  }

  onAddStore(): void {
    this.router.navigate(['/store/add']);
  }

  getProgressStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'in progress':
      case 'in-progress':
        return 'status-in-progress';
      case 'pending':
        return 'status-pending';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return 'status-pending';
    }
  }

  formatPhoneNumber(phone: number): string {
    if (!phone) return 'N/A';
    const phoneStr = phone.toString();
    if (phoneStr.length === 10) {
      return `(${phoneStr.slice(0, 3)}) ${phoneStr.slice(3, 6)}-${phoneStr.slice(6)}`;
    }
    return phoneStr;
  }

  formatStoreTax(tax: number): string {
    if (tax === null || tax === undefined) return 'N/A';
    return `${tax}%`;
  }
}
