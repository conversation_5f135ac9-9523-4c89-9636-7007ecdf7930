export interface Store {
  storeMasterId: number;
  storeMasterCode: string;
  storeName: string;
  owner: string;
  phone: number;
  email: string;
  storeTax: number;
  vendors: number;
  items: number;
  storeHours: string;
  location: string;
  createdDate: string;
  progressStatus: string;
}

export interface StorePagination {
  pageIndex: number | null;
  pageSize: number | null;
  totalRecords: number;
  totalPages: number | null;
  hasNextPage: boolean | null;
  hasPreviousPage: boolean | null;
  searchText: string | null;
  isPaginated: boolean;
  sortColumn: string | null;
  sortOrder: string;
}

export interface GetStoresApiResponse {
  stores: Store[];
  pagination: StorePagination;
}

export interface StoreQueryParams {
  pageIndex?: number;
  pageSize?: number;
  searchText?: string;
  isPaginated?: boolean;
  sortColumn?: string;
  sortOrder?: string;
}

export interface StoreFilterOptions {
  progressStatus: string[];
  location: string[];
}
