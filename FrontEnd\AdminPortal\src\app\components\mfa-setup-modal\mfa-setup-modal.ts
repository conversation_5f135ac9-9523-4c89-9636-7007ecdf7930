import { Component, inject } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';

@Component({
  selector: 'app-mfa-setup-modal',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './mfa-setup-modal.html',
  styleUrl: './mfa-setup-modal.scss'
})
export class MfaSetupModal {
  private dialogRef = inject(MatDialogRef<MfaSetupModal>);
  private router = inject(Router);

  onSkip(): void {
    this.dialogRef.close('skip');
  }

  onSetupMfa(): void {
    this.dialogRef.close('setup');
  }
}
