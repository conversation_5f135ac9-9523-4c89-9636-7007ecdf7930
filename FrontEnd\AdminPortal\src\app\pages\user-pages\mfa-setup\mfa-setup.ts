import { Component, signal, computed, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { MatStepperModule } from '@angular/material/stepper';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormInput } from '@components/form-input/form-input';
import { AuthService } from '@services/auth.service';
import { FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-mfa-setup',
  standalone: true,
  imports: [
    MatInputModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatStepperModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    FormInput,
    PrimaryButton
  ],
  templateUrl: './mfa-setup.html',
  styleUrl: './mfa-setup.scss'
})
export class MfaSetup {
  isLinear = true;
  private platformId = inject(PLATFORM_ID);

  // Step management 
  isLoading = signal<boolean>(false);
  errorMessage = signal<string>('');
  successMessage = signal<string>('');

  // MFA setup data
  qrCodeImage = signal<string>('');
  secretKey = signal<string>('');
  verificationCode = signal<string>('');

  // User email from auth service
  userEmail = computed(() => this.authService.currentUser()?.email || '');

  // Form validation
  isValidVerificationCode = computed(() => {
    const code = this.verificationCode();
    return code.length === 6 && /^\d{6}$/.test(code);
  });

  constructor(private authService: AuthService, private router: Router, private toastService: ToastService) { }

  onVerificationCodeChange(value: string): void {
    this.verificationCode.set(value);
    this.errorMessage.set('');
  }

  async setupTwoFactor(): Promise<void> {
    const email = this.userEmail();
    if (!email) {
      this.errorMessage.set('User email not found. Please login again.');
      return;
    }

    this.isLoading.set(true);
    this.errorMessage.set('');

    try {
      const result = await this.authService.setupTwoFactor(email);

      if (result.success && result.qrCode && result.secretKey) {
        this.qrCodeImage.set(result.qrCode);
        this.secretKey.set(result.secretKey);

      } else {
        this.errorMessage.set(result.message || 'Failed to setup two-factor authentication');
      }
    } catch (error) {
      this.errorMessage.set('An unexpected error occurred. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }

  async enableMfa(): Promise<void> {
    const email = this.userEmail();
    const otp = this.verificationCode();

    if (!email || !this.isValidVerificationCode()) {
      this.toastService.error('Please enter a valid 6-digit code!');

      return;
    }

    this.isLoading.set(true);
    this.errorMessage.set('');

    try {
      const result = await this.authService.enableTwoFactor({
        email,
        otp
      });

      if (result.success) {
        this.toastService.success('MFA has been successfully enabled for your account!');
        this.router.navigate(['/']);
      } else {
        this.toastService.error(result.message || 'Invalid verification code. Please try again.');

        this.isLoading.set(false);
      }
    } catch (error) {
      this.isLoading.set(false);
      this.toastService.error('An unexpected error occurred. Please try again.');
    }
  }

  onCancel(): void {
    this.router.navigate(['/']);
  }

  copySecretKey(): void {
    if (isPlatformBrowser(this.platformId)) {
      navigator.clipboard.writeText(this.secretKey()).then(() => {
        this.toastService.info('Secret key copied to clipboard');

      });
    }
  }
}
