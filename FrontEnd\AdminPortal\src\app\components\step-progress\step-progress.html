<div class="step-progress-container">
  <div class="progress-header">
    <h3>Progress</h3>
  </div>
  
  <div class="steps-list">
    @for (step of steps; track step.id; let i = $index) {
      <div class="step-item" [class]="'step-' + step.status">
        <!-- Step Icon -->
        <div class="step-icon-container">
          @if (step.status === 'completed') {
            <div class="step-icon completed">
              <mat-icon class="material-symbols">check</mat-icon>
            </div>
          } @else if (step.status === 'current') {
            <div class="step-icon current">
              <mat-icon class="material-symbols">{{ step.icon }}</mat-icon>
            </div>
          } @else {
            <div class="step-icon pending">
              <mat-icon class="material-symbols">{{ step.icon }}</mat-icon>
            </div>
          }
          
          <!-- Connector Line -->
          @if (i < steps.length - 1) {
            <div class="step-connector" 
                 [class.completed]="step.status === 'completed'">
            </div>
          }
        </div>
        
        <!-- Step Content -->
        <div class="step-content">
          <h4 class="step-title">{{ step.title }}</h4>
          <p class="step-description">{{ step.description }}</p>
          @if (step.status === 'current') {
            <span class="current-step-label">Current Step</span>
          }
        </div>
      </div>
    }
  </div>
</div>
