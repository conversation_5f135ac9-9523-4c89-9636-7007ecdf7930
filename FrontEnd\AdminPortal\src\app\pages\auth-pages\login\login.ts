import { Component, signal, computed, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AuthCard } from '@components/auth-card/auth-card';
import { FormInput } from '@components/form-input/form-input';
import { PasswordInput } from '@components/password-input/password-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { MfaSetupModal } from '@components/mfa-setup-modal/mfa-setup-modal';
import { LoginModel, LoginResponseModel } from '@models/auth-model/LoginModel';
import { AuthService } from '@services/auth.service';
import { ToastService } from '@services/toast.service';
import { ApiBaseResponse } from '@models/ApiResponse';

@Component({
  selector: 'app-login',
  imports: [
    AuthCard,
    FormInput,
    PasswordInput,
    PrimaryButton
  ],
  templateUrl: './login.html',
  styleUrl: './login.scss'
})
export class Login {
  userName = signal<string>('');
  password = signal<string>('');
  errorMessage = signal<string>('');

  loginData = computed<LoginModel>(() => ({
    userName: this.userName(),
    password: this.password()
  }));

  isValidForm = computed<boolean>(() =>
    !!(this.userName() && this.password())
  );

  // Get loading state from AuthService
  isLoading = computed(() => this.authService.isLoading());

  constructor(
    private authService: AuthService,
    private router: Router,
    private dialog: MatDialog,
    private toastService: ToastService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { }

  onUserNameChange(value: string): void {
    this.userName.set(value);
  }

  onPasswordChange(value: string): void {
    this.password.set(value);
  }

  async onLogin(): Promise<void> {
    if (this.isValidForm()) {
      this.errorMessage.set('');

      try {
        const result = await this.authService.login(this.loginData());

        if (result.isSuccess) {
          // Check MFA status after successful login
          if (result.data?.user.twoFactorEnabled) {
            this.redirectToMFA();
          } else {
            this.showMfaSetupModal(result);
          }
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('An unexpected error occurred. Please try again.');
      }
    }
  }

  onForgotPassword(event: Event): void {
    event.preventDefault();
    this.router.navigate(['/auth/forgot-password']);
  }

  private showMfaSetupModal(response: ApiBaseResponse<LoginResponseModel>): void {
    const dialogRef = this.dialog.open(MfaSetupModal, {
      width: '500px',
      maxWidth: '90vw',
      disableClose: true,
      panelClass: 'mfa-setup-modal-panel'
    });

    dialogRef.afterClosed().subscribe(result => { 
      this.toastService.success(response.message||'Login successful! Welcome back.'); 
      if (result === 'setup') {
        this.redirectToMFASetUp();
        return;
      }
      this.redirectToApp();
    });
  }

  private redirectToApp(): void {
    let redirectUrl = '/';
    if (isPlatformBrowser(this.platformId)) {
      redirectUrl = localStorage.getItem('invofy_redirect_url') || '/';
      localStorage.removeItem('invofy_redirect_url');
    }
    this.router.navigate([redirectUrl]);
  }

  private redirectToMFA(): void {
    let redirectUrl = '/auth/mfa';
    this.router.navigate([redirectUrl]);
  }

  private redirectToMFASetUp(): void {
    let redirectUrl = '/mfa-setup';
    this.router.navigate([redirectUrl]);
  }
}
