import { Component, computed, inject, signal } from '@angular/core';
import { MatSnackBarRef, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ToastConfirmData } from '@models/ToastModel'; 
import {MatFormFieldModule} from '@angular/material/form-field'; 
import {MatInputModule} from '@angular/material/input';
@Component({
  selector: 'app-toast-confirm',
  imports: [
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule
  ],
  templateUrl: "toast-confirm.html",
  styleUrl: './toast-confirm.scss'
})
export class ToastConfirm {
  private snackBarRef = inject(MatSnackBarRef);
  protected data: ToastConfirmData = inject(MAT_SNACK_BAR_DATA);
  protected confirmationText = signal<string>('');
  isDisabled = computed(() =>  this.data.confirmationType === 'CONFIRM' || this.confirmationText() === 'DELETE');

  protected getIcon(): string {
    switch (this.data.type) { 
      case 'warning':
        return 'warning'; 
      default:
        return 'warning';
    }
  }

  protected onAction(): void {
    this.snackBarRef.dismissWithAction();
  }

  protected onClose(): void {
    this.snackBarRef.dismiss();
  }
  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const newValue = inputElement.value;
    this.confirmationText.set(newValue); 
  }
}
