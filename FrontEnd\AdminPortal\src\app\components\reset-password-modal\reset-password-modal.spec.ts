import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { ResetPasswordModal, ResetPasswordModalData } from './reset-password-modal';
import { AuthService } from '@services/auth.service';
import { ToastService } from '@services/toast.service';
import { User } from '@models/UserModel';

describe('ResetPasswordModal', () => {
  let component: ResetPasswordModal;
  let fixture: ComponentFixture<ResetPasswordModal>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<ResetPasswordModal>>;

  const mockUser: User = {
    userId: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'Active',
    mfa: 'Enabled',
    createdDate: '2024-01-01',
    lastLogin: '2024-01-15'
  };

  const mockDialogData: ResetPasswordModalData = {
    user: mockUser
  };

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'forgotPassword',
      'verifyOtp',
      'resetPassword'
    ]);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', [
      'success',
      'error'
    ]);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        ResetPasswordModal,
        NoopAnimationsModule
      ],
      providers: [
        FormBuilder,
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ResetPasswordModal);
    component = fixture.componentInstance;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<ResetPasswordModal>>;

    // Setup default service responses
    mockAuthService.forgotPassword.and.returnValue(Promise.resolve({ success: true, message: 'OTP sent' }));
    mockAuthService.verifyOtp.and.returnValue(Promise.resolve({ success: true, message: 'OTP verified', token: 'test-token' }));
    mockAuthService.resetPassword.and.returnValue(Promise.resolve({ success: true, message: 'Password reset' }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with user data', () => {
    expect(component.data.user).toEqual(mockUser);
    expect(component.currentStep()).toBe(0);
  });

  it('should send initial OTP on component creation', async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    expect(mockAuthService.forgotPassword).toHaveBeenCalledWith({
      email: mockUser.email
    });
    expect(mockToastService.success).toHaveBeenCalledWith(`OTP sent to ${mockUser.email}`);
  });

  it('should handle initial OTP send failure', async () => {
    mockAuthService.forgotPassword.and.returnValue(Promise.resolve({ success: false, message: 'Failed to send OTP' }));
    
    fixture.detectChanges();
    await fixture.whenStable();

    expect(mockToastService.error).toHaveBeenCalledWith('Failed to send OTP');
    expect(mockDialogRef.close).toHaveBeenCalled();
  });

  it('should validate OTP correctly', () => {
    fixture.detectChanges();
    
    // Invalid OTP
    component.otpForm.get('otp')?.setValue('123');
    expect(component.isOtpValid()).toBe(false);
    
    // Valid OTP
    component.otpForm.get('otp')?.setValue('123456');
    expect(component.isOtpValid()).toBe(true);
  });

  it('should verify OTP successfully', async () => {
    fixture.detectChanges();
    
    component.otpForm.get('otp')?.setValue('123456');
    
    await component.onVerifyOtp();
    
    expect(mockAuthService.verifyOtp).toHaveBeenCalledWith({
      email: mockUser.email,
      otp: '123456'
    });
    expect(component.currentStep()).toBe(1);
    expect(component.otpToken()).toBe('test-token');
    expect(mockToastService.success).toHaveBeenCalledWith('OTP verified successfully');
  });

  it('should handle OTP verification failure', async () => {
    mockAuthService.verifyOtp.and.returnValue(Promise.resolve({ success: false, message: 'Invalid OTP' }));
    
    fixture.detectChanges();
    component.otpForm.get('otp')?.setValue('123456');
    
    await component.onVerifyOtp();
    
    expect(component.currentStep()).toBe(0);
    expect(mockToastService.error).toHaveBeenCalledWith('Invalid OTP');
  });

  it('should not verify invalid OTP', async () => {
    fixture.detectChanges();
    
    component.otpForm.get('otp')?.setValue('123'); // Invalid OTP
    
    await component.onVerifyOtp();
    
    expect(mockAuthService.verifyOtp).not.toHaveBeenCalled();
    expect(mockToastService.error).toHaveBeenCalledWith('Please enter a valid 6-digit OTP');
  });

  it('should validate password correctly', () => {
    fixture.detectChanges();
    
    // Invalid password
    component.passwordForm.get('password')?.setValue('weak');
    component.passwordForm.get('confirmPassword')?.setValue('weak');
    expect(component.isPasswordValid()).toBe(false);
    
    // Valid password
    component.passwordForm.get('password')?.setValue('StrongPass123!');
    component.passwordForm.get('confirmPassword')?.setValue('StrongPass123!');
    expect(component.isPasswordValid()).toBe(true);
  });

  it('should reset password successfully', async () => {
    fixture.detectChanges();
    
    component.otpToken.set('test-token');
    component.currentStep.set(1);
    component.passwordForm.get('password')?.setValue('StrongPass123!');
    component.passwordForm.get('confirmPassword')?.setValue('StrongPass123!');
    
    await component.onResetPassword();
    
    expect(mockAuthService.resetPassword).toHaveBeenCalledWith({
      token: 'test-token',
      newPassword: 'StrongPass123!'
    });
    expect(mockToastService.success).toHaveBeenCalledWith(`Password reset successfully for ${mockUser.name}`);
    expect(mockDialogRef.close).toHaveBeenCalledWith({ success: true });
  });

  it('should handle password reset failure', async () => {
    mockAuthService.resetPassword.and.returnValue(Promise.resolve({ success: false, message: 'Reset failed' }));
    
    fixture.detectChanges();
    component.otpToken.set('test-token');
    component.currentStep.set(1);
    component.passwordForm.get('password')?.setValue('StrongPass123!');
    component.passwordForm.get('confirmPassword')?.setValue('StrongPass123!');
    
    await component.onResetPassword();
    
    expect(mockToastService.error).toHaveBeenCalledWith('Reset failed');
  });

  it('should resend OTP successfully', async () => {
    fixture.detectChanges();
    
    await component.onResendOtp();
    
    expect(mockAuthService.forgotPassword).toHaveBeenCalledWith({
      email: mockUser.email
    });
    expect(mockToastService.success).toHaveBeenCalledWith('OTP resent successfully');
  });

  it('should handle resend OTP failure', async () => {
    mockAuthService.forgotPassword.and.returnValue(Promise.resolve({ success: false, message: 'Resend failed' }));
    
    fixture.detectChanges();
    
    await component.onResendOtp();
    
    expect(mockToastService.error).toHaveBeenCalledWith('Resend failed');
  });

  it('should navigate back to OTP step', () => {
    fixture.detectChanges();
    
    component.currentStep.set(1);
    component.onBackToOtp();
    
    expect(component.currentStep()).toBe(0);
  });

  it('should close dialog on cancel', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalled();
  });

  it('should validate password strength correctly', () => {
    // Test private method through component behavior
    fixture.detectChanges();
    
    // Weak passwords
    component.passwordForm.get('password')?.setValue('weak');
    expect(component.isPasswordValid()).toBe(false);
    
    component.passwordForm.get('password')?.setValue('WeakPassword');
    expect(component.isPasswordValid()).toBe(false);
    
    // Strong password
    component.passwordForm.get('password')?.setValue('StrongPass123!');
    component.passwordForm.get('confirmPassword')?.setValue('StrongPass123!');
    expect(component.isPasswordValid()).toBe(true);
  });

  it('should handle password mismatch', () => {
    fixture.detectChanges();
    
    component.passwordForm.get('password')?.setValue('StrongPass123!');
    component.passwordForm.get('confirmPassword')?.setValue('DifferentPass123!');
    
    expect(component.isPasswordValid()).toBe(false);
  });

  it('should start and manage resend countdown', (done) => {
    fixture.detectChanges();
    
    // Manually trigger countdown
    component['startResendCountdown']();
    
    expect(component.resendCountdown()).toBe(60);
    expect(component.isResendDisabled()).toBe(true);
    
    // Wait a bit and check countdown decreases
    setTimeout(() => {
      expect(component.resendCountdown()).toBeLessThan(60);
      done();
    }, 1100);
  });

  it('should provide correct error messages', () => {
    fixture.detectChanges();
    
    // OTP error
    component.otpForm.get('otp')?.setValue('');
    component.otpForm.get('otp')?.markAsTouched();
    expect(component.getOtpError()).toBe('OTP is required');
    
    // Password error
    component.passwordForm.get('password')?.setValue('');
    component.passwordForm.get('password')?.markAsTouched();
    expect(component.getPasswordError()).toBe('Password is required');
    
    // Confirm password error
    component.passwordForm.get('confirmPassword')?.setValue('');
    component.passwordForm.get('confirmPassword')?.markAsTouched();
    expect(component.getConfirmPasswordError()).toBe('Confirm password is required');
  });
});
