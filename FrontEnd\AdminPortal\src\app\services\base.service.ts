import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import type { ApiBaseResponse } from '@models/ApiResponse';
import { environment } from '../../environments/environment';

export interface RequestOptions {
    headers?: HttpHeaders | { [header: string]: string | string[] };
    params?: HttpParams | { [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean> };
    responseType?: 'json';
    withCredentials?: boolean;
}

@Injectable({
    providedIn: 'root'
})
export class BaseService {

    constructor(private http: HttpClient) { }

    async get<T>(url: string, options?: RequestOptions): Promise<ApiBaseResponse<T>> {
        return new Promise<ApiBaseResponse<T>>((resolve, reject) => {
            this.http.get<ApiBaseResponse<T>>(
            this.buildUrl(url),
            this.buildHttpOptions(options)
        ).pipe(
            catchError(this.handleError.bind(this))
        ).subscribe({
                next: (data) => resolve(data),
                error: (error) => reject(error)
            });
        });
    }

    async post<T>(url: string, payload?: any, options?: RequestOptions): Promise<ApiBaseResponse<T>> {
        return new Promise<ApiBaseResponse<T>>((resolve, reject) => {
            this.http.post<ApiBaseResponse<T>>(
            this.buildUrl(url),
            payload,
            this.buildHttpOptions(options)
        ).pipe(
            catchError(this.handleError.bind(this))
        ).subscribe({
                next: (data) => resolve(data),
                error: (error) => reject(error)
            });
        });
    } 

    async put<T>(url: string, payload: any, options?: RequestOptions): Promise<ApiBaseResponse<T>> {
        return new Promise<ApiBaseResponse<T>>((resolve, reject) => {
            this.http.put<ApiBaseResponse<T>>(
            this.buildUrl(url),
            payload,
            this.buildHttpOptions(options)
        ).pipe(
            catchError(this.handleError.bind(this))
        ).subscribe({
                next: (data) => resolve(data),
                error: (error) => reject(error)
            });
        });
    } 

    async patch<T>(url: string, payload: any, options?: RequestOptions): Promise<ApiBaseResponse<T>> {
        return new Promise<ApiBaseResponse<T>>((resolve, reject) => {
            this.http.patch<ApiBaseResponse<T>>(
            this.buildUrl(url),
            payload,
            this.buildHttpOptions(options)
        ).pipe(
            catchError(this.handleError.bind(this))
        ).subscribe({
                next: (data) => resolve(data),
                error: (error) => reject(error)
            });
        });
    }

    async delete<T>(url: string, options?: RequestOptions): Promise<ApiBaseResponse<T>> {
        return new Promise<ApiBaseResponse<T>>((resolve, reject) => {
                this.http.delete<ApiBaseResponse<T>>(
                    this.buildUrl(url),
                    this.buildHttpOptions(options)
                ).pipe(
                    catchError(this.handleError.bind(this))
                ).subscribe({
                        next: (data) => resolve(data),
                        error: (error) => reject(error)
                });
            });
    }
    async deleteWithBody<T>(url: string, payload: any, options?: RequestOptions): Promise<ApiBaseResponse<T>> {
        return new Promise<ApiBaseResponse<T>>((resolve, reject) => {
            const httpOptions = this.buildHttpOptions(options);
            httpOptions.body = payload;
            this.http.delete<ApiBaseResponse<T>>(
                this.buildUrl(url),
                httpOptions
            ).pipe(
                catchError(this.handleError.bind(this))
            ).subscribe({
                next: (data) => resolve(data),
                error: (error) => reject(error)
            });
        });
    }

    private buildUrl(url: string): string {
        const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
        return `${environment.apiBaseUrl}/${cleanUrl}`;
    }

    private buildHttpOptions(options?: RequestOptions): {
        headers?: HttpHeaders | { [header: string]: string | string[] };
        params?: HttpParams | { [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean> };
        responseType?: 'json';
        withCredentials?: boolean;
        body?: any;
    } {
        const defaultHeaders = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        };

        return {
            headers: options?.headers || defaultHeaders,
            params: options?.params,
            responseType: (options?.responseType || 'json') as 'json',
            withCredentials: options?.withCredentials || false
        };
    }

    private handleError(error: HttpErrorResponse): Observable<never> {
        let errorMessage = 'An unexpected error occurred';
        let statusCode = 500;

        if (error.error instanceof ErrorEvent) {
            errorMessage = `Client Error: ${error.error.message}`;
        } else {
            statusCode = error.status;
            if (error.status === 0) {
                errorMessage = 'Unable to connect to server. Please check your connection.';
            } else if (error.status === 401) {
                errorMessage = 'Unauthorized access. Please login again.';
            } else if (error.status === 403) {
                errorMessage = 'Access forbidden. You do not have permission to perform this action.';
            } else if (error.status === 404) {
                errorMessage = 'The requested resource was not found.';
            } else if (error.status === 500) {
                errorMessage = 'Internal server error. Please try again later.';
            } else {
                errorMessage = error.error?.message || `Server Error: ${error.status}`;
            }
        }

        console.error('API Error:', {
            status: statusCode,
            message: errorMessage,
            url: error.url,
            error: error
        });

        return throwError(() => error);
    }
}