<div class="grid gap-4">
  <div class="grid gap-3">
    <label class="text-2xl font-medium text-ivy-dark-blue">Invoices</label>
    <div class="grid grid-cols-(--ivy-dashboard-card-columns) gap-4">
        <dashboard-invoice-card title="10" body="Invoices Pending Admin Approvals"></dashboard-invoice-card>
        <dashboard-invoice-card title="3" body="Invoices Pending Submission to Admin"></dashboard-invoice-card>
        <dashboard-invoice-card title="2" body="Invoices Pending Delivery"></dashboard-invoice-card>
        <dashboard-invoice-card title="2" body="Invoices that needs validation"></dashboard-invoice-card>
         
    </div>
  </div>
   <div class="flex flex-col xl:flex-row gap-4">
   <div class="grid flex-1 gap-3">
    
    <label class="text-2xl font-medium text-ivy-dark-blue">Products</label>
    <div class="grid grid-cols-(--ivy-dashboard-card-columns) gap-4">
        <dashboard-invoice-card title="10" body="New Products added by Employee" tagText="Need to Approve"></dashboard-invoice-card>
        <dashboard-invoice-card title="3" body="Compare Prices by Products"></dashboard-invoice-card>
         
    </div>
    </div>
    <div class="grid flex-1 gap-3">
    
    <label class="text-2xl font-medium text-ivy-dark-blue">Reports</label>
    <div class="grid grid-cols-(--ivy-dashboard-card-columns) gap-4">
        <dashboard-invoice-card title="10" body="New Item and Price & Update Report" tagText="Exported: Jun 22, 2025"></dashboard-invoice-card>
        <dashboard-invoice-card title="10" body="New Vendor Product & Price Update Report" tagText="Exported: Jun 22, 2025"></dashboard-invoice-card>
         
    </div>
    </div>
  </div>

  <div class="grid gap-3">
    <label class="text-2xl font-medium text-ivy-dark-blue">Recent Invoices</label>
    <div class="grid grid-cols-(--ivy-dashboard-recent-invoice-card-columns) gap-4">
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        <dashboard-recent-invoice-card name="John Smith" invoiceNumber="#123456" invoiceDate="Jun 22, 2025" status="Paid" invoiceAmount="$1,234.56"></dashboard-recent-invoice-card>
        
  </div>
</div>