import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PasswordInput } from './password-input';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('PasswordInput', () => {
  let component: PasswordInput;
  let fixture: ComponentFixture<PasswordInput>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PasswordInput, NoopAnimationsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PasswordInput);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle password visibility', () => {
    expect(component.showPassword()).toBeFalse();

    component.togglePassword();
    expect(component.showPassword()).toBeTrue();

    component.togglePassword();
    expect(component.showPassword()).toBeFalse();
  });

  it('should emit value change on input', () => {
    let emittedValue: string | undefined;
    component.valueChange.subscribe(value => emittedValue = value);

    const inputElement = fixture.nativeElement.querySelector('input');
    inputElement.value = 'password123';
    inputElement.dispatchEvent(new Event('input'));

    expect(emittedValue).toBe('password123');
  });

  it('should change input type when password visibility is toggled', () => {
    const inputElement = fixture.nativeElement.querySelector('input');

    expect(inputElement.type).toBe('password');

    component.togglePassword();
    fixture.detectChanges();

    expect(inputElement.type).toBe('text');
  });
});
