export interface CreateUserModel {
  firstName: string;
  lastName: string;
  userName: string;
  email: string;
  confirmEmail: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
}

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  userName: string;
  email: string;
  confirmEmail: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
}

export interface CreateUserResponse {
  isSuccess: boolean;
  message: string;
  data?: {
    userId: string;
    email: string;
    verificationRequired: boolean;
  };
  statusCode: number;
}

export interface EmailVerificationRequest {
  email: string;
  verificationCode: string;
}

export interface EmailVerificationResponse {
  isSuccess: boolean;
  message: string;
  statusCode: number;
}

export interface ResendCodeRequest {
  email: string;
}

export interface ResendCodeResponse {
  isSuccess: boolean;
  message: string;
  statusCode: number;
}

export interface DeleteUserRequest {
  userId: number;
}

export interface DeleteUserResponse {
  isSuccess: boolean;
  message: string;
  statusCode: number;
}

export interface GetUserByIdRequest {
  userId: number;
}

export interface GetUserByIdResponse { 
    userId: number;
    firstName: string;
    lastName: string;
    userName: string;
    email: string;
    phoneNumber: string;
    accountStatus: string;
    createdOn: string;
    lastLoggedIn: string;
    mfaEnabled: boolean; 
}

export interface UpdateUserRequest {
  userId: number;
  firstName: string;
  lastName: string;
  userName: string;
  phoneNumber: string;
  accountStatus: string;
}

export interface UpdateUserResponse {
  isSuccess: boolean;
  message: string;
  statusCode: number;
}

export interface EditUserRequest {
  userCode: string;
  firstName: string;
  lastName: string;
  userName: string;
  phoneNumber: string;
}

export interface SendEmailChangeOtpRequest {
  userId: string;
  newEmail: string;
}

export interface SendEmailChangeOtpResponse {
  userId: string;
  newEmail: string;
}

export interface VerifyEmailChangeNewRequest {
  userId: string;  // Actually expects userCode (GUID)
  newEmail: string;
  otp: string;
}

export interface VerifyEmailChangeNewResponse {
  userId: string;  // Returns userCode (GUID)
  newEmail: string;
  otp: string;
}

export interface UpdateEmailRequest {
  userId: number;
  newEmail: string;
}

export interface UpdateEmailResponse {
  isSuccess: boolean;
  message: string;
  data?: {
    verificationRequired: boolean;
    verificationCode?: string;
  };
  statusCode: number;
}

export interface VerifyEmailChangeRequest {
  userId: number;
  email: string;
  verificationCode: string;
}

export interface VerifyEmailChangeResponse {
  isSuccess: boolean;
  message: string;
  statusCode: number;
}

export interface ResetPasswordRequest {
  userId: number;
}

export interface ResetPasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: {
    temporaryPassword: string;
  };
  statusCode: number;
}

export interface UpdateMfaStatusRequest {
  userId: number;
  isEnabled: boolean;
}

export interface UpdateMfaStatusResponse {
  isSuccess: boolean;
  message: string;
  statusCode: number;
}

export interface MarkAsInactiveRequest {
  userCode: string;
  isActive: boolean;
}

export interface MarkAsInactiveResponse {
  isSuccess: boolean;
  message: string;
  data: null;
  statusCode: number;
}

export interface GetUserByUserCodeRequest {
  userCode: string;
}

export interface GetUserByUserCodeResponse { 
    userId: number;
    userCode: string;
    userName: string;
    password: string;
    userTypeId: number;
    storeMasterId: number;
    firstName: string;
    lastName: string;
    address: string | null;
    phoneNumber: string;
    email: string;
    sendSMS: boolean;
    otpForSMS: boolean;
    invofyEndUser_UserId: number;
    twoFactorEnabled: boolean;
    twoFactorSecret: string;
    userTypeName: string | null; 
}
