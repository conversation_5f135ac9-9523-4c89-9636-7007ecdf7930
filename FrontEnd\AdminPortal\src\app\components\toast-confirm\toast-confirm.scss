
.toast-container { 
  align-items: center;
  min-width: 300px;
  max-width: 500px; 
  border-radius: 8px; 
  overflow: hidden;

  .toast-content { 
    align-items: center;
    width: 100%;
    padding:  20px 20px 6px;
    gap: 12px;
  }

  .toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    .toast-icon-container{
      background-color: var(--ivy-warning-light);
    }
    .toast-icon-symbol {
      font-size: 25px;
      width: 25px;
      height: 25px;
      margin-top: -4px;
      color: var(--ivy-warning-dark);

    }
  }

  .toast-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
  }

  .toast-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
    justify-content: flex-end;
        border-top: 1px solid;
    .toast-action-button {
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      min-width: auto;
      padding: 4px 8px 1px;
      height: 32px;
      margin-top : 6px ; 
    }

    .toast-close-button {
      width: 32px;
      height: 32px;
      
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }


  &.toast-warning {
    background-color: var(--ivy-base-white);
 
  
    .toast-action-button {
      border-color: rgba(255, 255, 255, 0.3);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .toast-close-button { 

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

}

// Responsive design
@media (max-width: 768px) {
  .toast-container {
    min-width: 280px;
    max-width: 90vw;

    .toast-content {
      padding: 10px 12px;
      gap: 8px;
    }

    .toast-message {
      font-size: 13px;
    }

    .toast-actions {
      .toast-action-button {
        font-size: 11px;
        padding: 2px 6px;
        height: 28px;
      }

      .toast-close-button {
        width: 28px;
        height: 28px;
        
        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

