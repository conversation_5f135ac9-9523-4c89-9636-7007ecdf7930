{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/error-state-Dtb1IHM-.mjs"], "sourcesContent": ["/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n    _defaultMatcher;\n    ngControl;\n    _parentFormGroup;\n    _parentForm;\n    _stateChanges;\n    /** Whether the tracker is currently in an error state. */\n    errorState = false;\n    /** User-defined matcher for the error state. */\n    matcher;\n    constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n        this._defaultMatcher = _defaultMatcher;\n        this.ngControl = ngControl;\n        this._parentFormGroup = _parentFormGroup;\n        this._parentForm = _parentForm;\n        this._stateChanges = _stateChanges;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n        const oldState = this.errorState;\n        const parent = this._parentFormGroup || this._parentForm;\n        const matcher = this.matcher || this._defaultMatcher;\n        const control = this.ngControl ? this.ngControl.control : null;\n        const newState = matcher?.isErrorState(control, parent) ?? false;\n        if (newState !== oldState) {\n            this.errorState = newState;\n            this._stateChanges.next();\n        }\n    }\n}\n\nexport { _ErrorStateTracker as _ };\n\n"], "mappings": ";;;AAIA,IAAM,qBAAN,MAAyB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb;AAAA,EACA,YAAY,iBAAiB,WAAW,kBAAkB,aAAa,eAAe;AAClF,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAAA,EACzB;AAAA;AAAA,EAEA,mBAAmB;AACf,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK,oBAAoB,KAAK;AAC7C,UAAM,UAAU,KAAK,WAAW,KAAK;AACrC,UAAM,UAAU,KAAK,YAAY,KAAK,UAAU,UAAU;AAC1D,UAAM,WAAW,SAAS,aAAa,SAAS,MAAM,KAAK;AAC3D,QAAI,aAAa,UAAU;AACvB,WAAK,aAAa;AAClB,WAAK,cAAc,KAAK;AAAA,IAC5B;AAAA,EACJ;AACJ;", "names": []}