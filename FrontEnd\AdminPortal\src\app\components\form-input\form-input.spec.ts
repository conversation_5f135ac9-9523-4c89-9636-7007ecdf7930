import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormInput } from './form-input';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('FormInput', () => {
  let component: FormInput;
  let fixture: ComponentFixture<FormInput>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FormInput, NoopAnimationsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FormInput);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit value change on input', () => {
    let emittedValue: string | undefined;
    component.valueChange.subscribe(value => emittedValue = value);

    const inputElement = fixture.nativeElement.querySelector('input');
    inputElement.value = '<EMAIL>';
    inputElement.dispatchEvent(new Event('input'));

    expect(emittedValue).toBe('<EMAIL>');
  });

  it('should display icon when iconName is provided', () => {
    fixture.componentRef.setInput('iconName', 'email');
    fixture.detectChanges();

    const iconElement = fixture.nativeElement.querySelector('mat-icon');
    expect(iconElement).toBeTruthy();
    expect(iconElement.textContent).toContain('email');
  });
});
