.page-container {

    // Header Section
    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .header-left {
            .page-count {
                font-size: 24px;
                font-weight: 600;
                color: #333;
                margin: 0;
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;

            .search-input {
                width: 300px;

                .mat-mdc-form-field-subscript-wrapper {
                    display: none;
                }
            }


        }
    }

    // Error Message
    .error-message {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
        margin-bottom: 16px;

        mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
        }
    }

    // Loading Container
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px;
        color: #666;

        p {
            margin-top: 16px;
            font-size: 16px;
        }
    }

    // Table Container
    .table-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .data-table {
            width: 100%;

            // Header styling
            .mat-mdc-header-row {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;

                .mat-mdc-header-cell {
                    font-weight: 600;
                    color: #495057;
                    font-size: 14px;
                    padding: 16px 12px;
                    border-bottom: none;
                }
            }

            // Row styling
            .mat-mdc-row {
                border-bottom: 1px solid #dee2e6;

                &:hover {
                    background-color: #f8f9fa;
                }

                .mat-mdc-cell {
                    padding: 16px 12px;
                    font-size: 14px;
                    color: #495057;
                    border-bottom: none;
                }
            }
        }

        // No data message
        .no-data-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 48px;
            text-align: center;
            color: #666;

            .no-data-icon {
                font-size: 48px;
                width: 48px;
                height: 48px;
                margin-bottom: 16px;
                color: #ccc;
            }

            h3 {
                margin: 0 0 8px 0;
                font-size: 18px;
                font-weight: 500;
            }

            p {
                margin: 0;
                color: #999;
            }
        }

        .status-active {
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-inactive {
            background-color: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-deactivated {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-locked {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        // MFA status styling
        .mfa-status {
            display: flex;
            align-items: center;
            gap: 6px;

            .mfa-icon {
                font-size: 16px;

                &.mfa-enabled {
                    color: #28a745;
                }

                &.mfa-disabled {
                    color: #6c757d;
                }
            }
        }


        
    }

    // Angular Material Paginator styling
    mat-paginator {
        background-color: white;
        border-top: 1px solid #dee2e6;
    }
}

// Responsive design
@media (max-width: 768px) {
    .page-container {
        padding: 16px;

        .header-section {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;

            .header-right {
                justify-content: space-between;

                .search-input {
                    flex: 1;
                    margin-right: 16px;
                }
            }
        }
    }
}