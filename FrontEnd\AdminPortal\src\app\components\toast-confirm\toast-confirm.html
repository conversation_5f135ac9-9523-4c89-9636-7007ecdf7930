<div class="toast-container" [class]="'toast-' + data.type">
    <div class="toast-content">
        
        <div class="flex  gap-4">
            <div class="!text-ivy-dark-blue toast-icon ">
                <div class="flex justify-center items-center h-12 w-12   rounded-full toast-icon-container" >
                <mat-icon   class="material-symbols toast-icon-symbol">{{ getIcon() }}</mat-icon>
                </div>  
            </div>
            <div class="!text-ivy-dark-blue toast-message ">
                <h2 class="font-bold text-xl"> {{ data.title  }}</h2>
                <p> {{ data.message }}</p>
                <div class="mt-2">
                    @if (data.confirmationType === 'DELETE') {
                        <p class="text-sm text-ivy-dark-blue">Please type 'DELETE' in below box to confirm.</p>
                        <input  (input)="onInputChange($event)" class="!border !border-gray-300  placeholder-gray-400  rounded-md p-2 w-full" placeholder="Type 'DELETE' here" type="text" />
                    }
                </div>
            </div>
        </div>
        
        <div class="toast-actions mt-3 ">
            @if (data.showCloseButton) {
                <button  mat-button class="toast-action-button" (click)="onClose()">
                    Close
                </button>
            }
            @if (data.action) {
                <button  [disabled]="!isDisabled()" mat-button class="toast-action-button" (click)="onAction()">
                    {{ data.action }}
                </button>
            }
        </div>
    </div>
</div>