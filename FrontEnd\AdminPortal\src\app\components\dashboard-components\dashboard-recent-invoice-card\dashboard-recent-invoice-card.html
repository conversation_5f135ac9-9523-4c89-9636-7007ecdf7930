 <mat-card class=" flex-grow  p-1 !border-none grid gap-2" appearance="outlined">
        <mat-card-header> 
            <span class="text-ivy-dark-blue font-normal text-sm"> {{invoiceNumber()}} | {{invoiceDate()}}</span>
        @if (status()) {
         <mat-basic-chip class="bg-ivy-light-white rounded-lg px-3 py-2 text-ivy-dark-blue text-xs size-fit font-medium top-3 right-3 !absolute">{{status()}}</mat-basic-chip>
        }
        </mat-card-header>
        <mat-card-content>
            <div class="flex justify-between">
            <p class="!text-ivy-dark-blue font-semibold text-base ">
            {{name()}}
        </p>
        <p class="!text-ivy-dark-blue font-bold text-base ">
            {{invoiceAmount()}}
        </p>
        </div> 
        </mat-card-content> 
</mat-card>