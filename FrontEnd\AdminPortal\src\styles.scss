@use "@angular/material" as mat;
@import url("./assets/themes/ivy-light-themes/ivy-themes.css");
@import url("./assets/themes/ivy-light-themes/tailwind.css");


$ivy-light-blue: var(--ivy-light-blue);
$ivy-dark-blue: var(--ivy-dark-blue);
$ivy-light-white: var(--ivy-light-white);
$ivy-base-white: var(--ivy-base-white);
$ivy-sky-blue: var(--ivy-sky-blue);
$ivy-orange-blue: var(--ivy-orange-blue);


html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    font-family: Roboto, "Helvetica Neue", sans-serif;
    background-color: var(--ivy-light-white);
    color: var(--ivy-text-primary);
    line-height: 1.5;
}

.auth-container>* {
    max-width: 100%;
}

/* MFA Setup Modal Styles */
.mfa-setup-modal-panel {
    .mat-mdc-dialog-container {
        border-radius: 12px;
        overflow: hidden;
    }
}
    
.mat-mdc-snackbar-surface {
    padding-right: 0px !important;
}
.mdc-snackbar__label { 
    padding: 0 !important;
}
.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface {
    border-radius: 8px !important;
}

.ivy-toast-confirm{
    width: 100vw;
    margin: 0 !important;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0000006b;
}