import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { StepProgress, StepItem } from '@components/step-progress/step-progress';
import { FormInput } from '@components/form-input/form-input';
import { PasswordInput } from '@components/password-input/password-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { ToastService } from '@services/toast.service';
import { UserService } from '@services/user.service';
import { CreateUserRequest, EmailVerificationRequest, ResendCodeRequest } from '@models/user-model/CreateUserModel';

@Component({
  selector: 'app-create-user',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    StepProgress,
    FormInput,
    PasswordInput,
    PrimaryButton
  ],
  styleUrls: ['./create-user.scss'],
  templateUrl:'create-user.html' 
})
export class CreateUser implements OnInit {
  currentStep = signal(0);
  isLoading = signal(false);
  createdUserEmail = signal(''); 
  firstName = signal('');
  lastName = signal('');
  userName = signal('');
  email = signal('');
  confirmEmail = signal('');
  password = signal('');
  confirmPassword = signal('');
  phoneNumber = signal('');
  verificationCode = signal('');

  steps: StepItem[] = [
    {
      id: 'user-info',
      title: 'User Information',
      description: 'Enter basic user details and credentials',
      icon: 'person',
      status: 'current'
    },
    {
      id: 'email-verification',
      title: 'Email Verification',
      description: 'Verify email address with OTP',
      icon: 'email',
      status: 'pending'
    }
  ];

  constructor(
    private router: Router,
    private toastService: ToastService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.updateStepStatus();
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(phone) && phone.length >= 10;
  }

  private isValidVerificationCode(code: string): boolean {
    return /^\d{6}$/.test(code);
  }

  private validateUserForm(): boolean {
    const errors: string[] = [];

    if (!this.firstName() || this.firstName().length < 2) {
      errors.push('First name must be at least 2 characters');
    }
    if (!this.lastName() || this.lastName().length < 2) {
      errors.push('Last name must be at least 2 characters');
    }
    if (!this.userName() || this.userName().length < 3) {
      errors.push('Username must be at least 3 characters');
    }
    if (!this.email() || !this.isValidEmail(this.email())) {
      errors.push('Please enter a valid email address');
    }
    if (this.email() !== this.confirmEmail()) {
      errors.push('Email addresses do not match');
    }
    if (!this.password() || this.password().length < 8) {
      errors.push('Password must be at least 8 characters');
    }
    if (this.password() !== this.confirmPassword()) {
      errors.push('Passwords do not match');
    }
    if (!this.phoneNumber() || !this.isValidPhone(this.phoneNumber())) {
      errors.push('Please enter a valid phone number');
    }

    if (errors.length > 0) {
      this.toastService.error(errors[0]);
      return false;
    }
    return true;
  }

  private updateStepStatus(): void {
    this.steps.forEach((step, index) => {
      if (index < this.currentStep()) {
        step.status = 'completed';
      } else if (index === this.currentStep()) {
        step.status = 'current';
      } else {
        step.status = 'pending';
      }
    });
  }

  onNextStep(): void {
    if (this.currentStep() === 0) {
      this.onCreateUser();
    } else if (this.currentStep() === 1) {
      this.onVerifyEmail();
    }
  }

  async onCreateUser() {
    if (!this.validateUserForm()) {
      return;
    }

    this.isLoading.set(true);

    const createUserRequest: CreateUserRequest = {
      firstName: this.firstName(),
      lastName: this.lastName(),
      userName: this.userName(),
      email: this.email(),
      confirmEmail: this.confirmEmail(),
      password: this.password(),
      confirmPassword: this.confirmPassword(),
      phoneNumber: this.phoneNumber()
    };

  const response= await this.userService.createUser(createUserRequest);
        if (response.isSuccess) {
          this.createdUserEmail.set(this.email());
          this.currentStep.set(1);
          this.updateStepStatus();
          this.toastService.success(response.message || 'User created successfully! Please verify the email address.');
        } else {
          this.toastService.error(response.message || 'Failed to create user. Please try again.');
        } 
  }

  async onVerifyEmail() {
    if (!this.verificationCode() || !this.isValidVerificationCode(this.verificationCode())) {
      this.toastService.error('Please enter a valid 6-digit verification code.');
      return;
    }

    this.isLoading.set(true);

    const verificationRequest: EmailVerificationRequest = {
      email: this.createdUserEmail(),
      verificationCode: this.verificationCode()
    };

    const response= await  this.userService.verifyUserEmail(verificationRequest) 
        if (response.isSuccess) {
          this.currentStep.set(2);
          this.updateStepStatus();
          this.toastService.success(response.message || 'Email verified successfully!');

          // Navigate back to users list after successful verification
          setTimeout(() => {
            this.router.navigate(['/admin-user']);
          }, 2000);
        } else {
          this.toastService.error(response.message || 'Invalid verification code. Please try again.');
        } 
  }

  async onResendCode() {
    this.isLoading.set(true);

    const resendRequest: ResendCodeRequest = {
      email: this.createdUserEmail()
    };

    const response= await this.userService.resendVerificationCode(resendRequest);
        if (response.isSuccess) {
          this.toastService.success(response.message || 'Verification code resent successfully!');
        } else {
          this.toastService.error(response.message || 'Failed to resend verification code. Please try again.');
        } 
  }

  onCancel(): void {
    this.router.navigate(['/user-management/admin-users']);
  }

  onBackToUsers(): void {
    this.router.navigate(['/admin-user/']);
  }
 
  onFirstNameChange(value: string): void {
    this.firstName.set(value);
  }

  onLastNameChange(value: string): void {
    this.lastName.set(value);
  }

  onUserNameChange(value: string): void {
    this.userName.set(value);
  }

  onEmailChange(value: string): void {
    this.email.set(value);
  }

  onConfirmEmailChange(value: string): void {
    this.confirmEmail.set(value);
  }

  onPasswordChange(value: string): void {
    this.password.set(value);
  }

  onConfirmPasswordChange(value: string): void {
    this.confirmPassword.set(value);
  }

  onPhoneNumberChange(value: string): void {
    this.phoneNumber.set(value);
  }

  onVerificationCodeChange(value: string): void {
    this.verificationCode.set(value);
  }
 
  get emailMismatch(): boolean {
    return this.email() !== this.confirmEmail() && this.confirmEmail().length > 0;
  }

  get passwordMismatch(): boolean {
    return this.password() !== this.confirmPassword() && this.confirmPassword().length > 0;
  }

  get isUserFormValid(): boolean {
      return this.firstName().length >= 2 &&
            this.lastName().length >= 2 &&
            this.userName().length >= 3 &&
            this.isValidEmail(this.email()) &&
            this.email() === this.confirmEmail() &&
            this.password().length >= 8 &&
            this.password() === this.confirmPassword() &&
            this.isValidPhone(this.phoneNumber());
  }

  get isVerificationFormValid(): boolean {
    return this.isValidVerificationCode(this.verificationCode());
  }
}
