import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatSnackBarRef, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Toast } from './toast';
import { ToastData } from '@models/ToastModel';

describe('Toast', () => {
  let component: Toast;
  let fixture: ComponentFixture<Toast>;
  let mockSnackBarRef: jasmine.SpyObj<MatSnackBarRef<Toast>>;
  let mockData: ToastData;

  beforeEach(async () => {
    mockSnackBarRef = jasmine.createSpyObj('MatSnackBarRef', ['dismiss', 'dismissWithAction']);
    mockData = {
      message: 'Test message',
      type: 'success',
      action: 'Undo',
      showCloseButton: true
    };

    await TestBed.configureTestingModule({
      imports: [Toast, NoopAnimationsModule],
      providers: [
        { provide: MatSnackBarRef, useValue: mockSnackBarRef },
        { provide: MAT_SNACK_BAR_DATA, useValue: mockData }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(Toast);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display the correct message', () => {
    const messageElement = fixture.nativeElement.querySelector('.toast-message');
    expect(messageElement.textContent.trim()).toBe('Test message');
  });

  it('should display the correct icon for success type', () => {
    expect(component['getIcon']()).toBe('check_circle');
  });

  it('should display the correct icon for error type', () => {
    component['data'].type = 'error';
    expect(component['getIcon']()).toBe('error');
  });

  it('should call dismissWithAction when action button is clicked', () => {
    const actionButton = fixture.nativeElement.querySelector('.toast-action-button');
    actionButton.click();
    expect(mockSnackBarRef.dismissWithAction).toHaveBeenCalled();
  });

  it('should call dismiss when close button is clicked', () => {
    const closeButton = fixture.nativeElement.querySelector('.toast-close-button');
    closeButton.click();
    expect(mockSnackBarRef.dismiss).toHaveBeenCalled();
  });

  it('should apply the correct CSS class based on type', () => {
    const container = fixture.nativeElement.querySelector('.toast-container');
    expect(container.classList.contains('toast-success')).toBe(true);
  });
});
