export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastConfig {
  message: string;
  type: ToastType;
  duration?: number;
  action?: string;
  showCloseButton?: boolean;
  position?: 'top' | 'bottom';
  horizontalPosition?: 'start' | 'center' | 'end' | 'left' | 'right';
}

export interface ToastData {
  message: string;
  type: ToastType;
  action?: string;
  showCloseButton?: boolean;
}


export interface ToastConfirmData {
  title: string;
  confirmationType : 'DELETE'|'CONFIRM';
  message: string;
  type: ToastType;
  action?: string;
  showCloseButton?: boolean;
}