import { Component, Inject, signal, computed, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatStepperModule } from '@angular/material/stepper';
import { Subject, takeUntil } from 'rxjs';

import { AuthService } from '@services/auth.service';
import { ToastService } from '@services/toast.service';
import { User } from '@models/UserModel';
import { ForgotPasswordModel } from '@models/auth-model/ForgotPasswordModel';
import { VerifyOtpModel } from '@models/auth-model/VerifyOtpModel';
import { ResetPasswordModel } from '@models/auth-model/ResetPasswordModel';
import { PrimaryButton } from '@components/primary-button/primary-button';

export interface ResetPasswordModalData {
  user: User;
}

@Component({
  selector: 'app-reset-password-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatStepperModule,
    PrimaryButton
  ],
  templateUrl: './reset-password-modal.html',
  styleUrls: ['./reset-password-modal.scss']
})
export class ResetPasswordModal implements OnDestroy {
  private destroy$ = new Subject<void>();

  // Signals
  currentStep = signal<number>(0);
  isLoading = signal<boolean>(false);
  otpToken = signal<string>('');
  resendCountdown = signal<number>(0);
  
  // Forms
  otpForm!: FormGroup;
  passwordForm!: FormGroup;
  
  // Signals for form values
  otpValue = signal<string>('');

  // Computed properties
  isOtpValid = computed(() => {
    const otp = this.otpValue();
    return otp && otp.length === 6 && /^\d{6}$/.test(otp);
  });

  // Signals for password form values
  passwordValue = signal<string>('');
  confirmPasswordValue = signal<string>('');

  isPasswordValid = computed(() => {
    const password = this.passwordValue();
    const confirmPassword = this.confirmPasswordValue();
    return password &&
           confirmPassword &&
           password === confirmPassword &&
           this.isPasswordStrong(password);
  });

  isResendDisabled = computed(() =>
    this.isLoading() || this.resendCountdown() > 0
  );

  resendButtonText = computed(() => {
    const countdown = this.resendCountdown();
    return countdown > 0 ? `Resend OTP (${countdown}s)` : 'Resend OTP';
  });

  // Password validation computed properties
  hasMinLength = computed(() => this.passwordValue().length >= 8);
  hasUppercase = computed(() => /[A-Z]/.test(this.passwordValue()));
  hasLowercase = computed(() => /[a-z]/.test(this.passwordValue()));
  hasNumber = computed(() => /\d/.test(this.passwordValue()));
  hasSpecialChar = computed(() => /[@$!%*?&]/.test(this.passwordValue()));

  private countdownInterval: any = null;

  constructor(
    public dialogRef: MatDialogRef<ResetPasswordModal>,
    @Inject(MAT_DIALOG_DATA) public data: ResetPasswordModalData,
    private fb: FormBuilder,
    private authService: AuthService,
    private toastService: ToastService
  ) {
    this.initializeForms();
    this.sendInitialOtp();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
  }

  private initializeForms(): void {
    this.otpForm = this.fb.group({
      otp: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });

    this.passwordForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    // Subscribe to OTP form value changes
    this.otpForm.get('otp')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.otpValue.set(value || '');
      });

    // Subscribe to password form value changes
    this.passwordForm.get('password')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.passwordValue.set(value || '');
      });

    this.passwordForm.get('confirmPassword')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.confirmPasswordValue.set(value || '');
      });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else {
      confirmPassword?.setErrors(null);
    }
    return null;
  }

  private async sendInitialOtp(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      const forgotPasswordData: ForgotPasswordModel = {
        email: this.data.user.email
      };

      const result = await this.authService.forgotPassword(forgotPasswordData);
      
      if (result.success) {
        this.toastService.success(`OTP sent to ${this.data.user.email}`);
        this.startResendCountdown();
      } else {
        this.toastService.error(result.message);
        this.dialogRef.close();
      }
    } catch (error) {
      this.toastService.error('Failed to send OTP. Please try again.');
      this.dialogRef.close();
    } finally {
      this.isLoading.set(false);
    }
  }

  async onVerifyOtp(): Promise<void> {
    if (!this.isOtpValid()) {
      this.toastService.error('Please enter a valid 6-digit OTP');
      return;
    }

    this.isLoading.set(true);

    try {
      const verifyOtpData: VerifyOtpModel = {
        email: this.data.user.email,
        otp: this.otpForm.get('otp')?.value
      };

      const result = await this.authService.verifyOtp(verifyOtpData);
      
      if (result.success && result.token) {
        this.otpToken.set(result.token);
        this.currentStep.set(1);
        this.toastService.success('OTP verified successfully');
      } else {
        this.toastService.error(result.message || 'Invalid OTP');
      }
    } catch (error) {
      this.toastService.error('Failed to verify OTP. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }

  async onResetPassword(): Promise<void> {
    if (!this.isPasswordValid()) {
      this.toastService.error('Please enter a valid password');
      return;
    }

    this.isLoading.set(true);

    try {
      const resetPasswordData: ResetPasswordModel = {
        token: this.otpToken(),
        newPassword: this.passwordForm.get('password')?.value
      };

      const result = await this.authService.resetPassword(resetPasswordData);
      
      if (result.success) {
        this.toastService.success(`Password reset successfully for ${this.data.user.name}`);
        this.dialogRef.close({ success: true });
      } else {
        this.toastService.error(result.message || 'Failed to reset password');
      }
    } catch (error) {
      this.toastService.error('Failed to reset password. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }

  async onResendOtp(): Promise<void> {
    if (this.isResendDisabled()) return;

    this.isLoading.set(true);

    try {
      const forgotPasswordData: ForgotPasswordModel = {
        email: this.data.user.email
      };

      const result = await this.authService.forgotPassword(forgotPasswordData);
      
      if (result.success) {
        this.toastService.success('OTP resent successfully');
        this.startResendCountdown();
      } else {
        this.toastService.error(result.message);
      }
    } catch (error) {
      this.toastService.error('Failed to resend OTP. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }

  private startResendCountdown(): void {
    this.resendCountdown.set(60); // 60 seconds countdown
    
    this.countdownInterval = setInterval(() => {
      const current = this.resendCountdown();
      if (current > 0) {
        this.resendCountdown.set(current - 1);
      } else {
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }
    }, 1000);
  }

  private isPasswordStrong(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(password);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onBackToOtp(): void {
    this.currentStep.set(0);
    this.passwordForm.reset();
    this.passwordValue.set('');
    this.confirmPasswordValue.set('');
  }

  // Helper methods for template
  getOtpError(): string {
    const otpControl = this.otpForm.get('otp');
    if (otpControl?.errors && otpControl.touched) {
      if (otpControl.errors['required']) return 'OTP is required';
      if (otpControl.errors['pattern']) return 'Please enter a valid 6-digit OTP';
    }
    return '';
  }

  getPasswordError(): string {
    const passwordControl = this.passwordForm.get('password');
    if (passwordControl?.errors && passwordControl.touched) {
      if (passwordControl.errors['required']) return 'Password is required';
      if (passwordControl.errors['minlength']) return 'Password must be at least 8 characters';
      if (!this.isPasswordStrong(passwordControl.value)) {
        return 'Password must contain uppercase, lowercase, number and special character';
      }
    }
    return '';
  }

  getConfirmPasswordError(): string {
    const confirmPasswordControl = this.passwordForm.get('confirmPassword');
    if (confirmPasswordControl?.errors && confirmPasswordControl.touched) {
      if (confirmPasswordControl.errors['required']) return 'Confirm password is required';
      if (confirmPasswordControl.errors['passwordMismatch']) return 'Passwords do not match';
    }
    return '';
  }
}
