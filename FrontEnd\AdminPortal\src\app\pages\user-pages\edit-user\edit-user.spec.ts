import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of, throwError } from 'rxjs';

import { EditUser } from './edit-user';
import { UserService } from '@services/user.service';
import { ToastService } from '@services/toast.service';
import { GetUserByIdResponse } from '@models/user-model/CreateUserModel';

describe('EditUser', () => {
  let component: EditUser;
  let fixture: ComponentFixture<EditUser>;
  let mockUserService: jasmine.SpyObj<UserService>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  const mockUserData = {
    userId: 1,
    firstName: 'John',
    lastName: 'Doe',
    userName: 'johndoe',
    email: '<EMAIL>',
    phoneNumber: '+**********',
    accountStatus: 'Active',
    createdOn: '2024-01-01T00:00:00Z',
    lastLoggedIn: '2024-01-15T10:30:00Z',
    mfaEnabled: true
  };

  const mockGetUserResponse: GetUserByIdResponse = {
    isSuccess: true,
    message: 'User retrieved successfully',
    data: mockUserData,
    statusCode: 200
  };

  beforeEach(async () => {
    const userServiceSpy = jasmine.createSpyObj('UserService', [
      'getUserById',
      'updateUser',
      'updateUserEmail',
      'verifyEmailChange',
      'resetUserPassword'
    ]);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', [
      'success',
      'error',
      'info',
      'confirm'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    mockActivatedRoute = {
      queryParams: of({ userId: '1' })
    };

    await TestBed.configureTestingModule({
      imports: [
        EditUser,
        NoopAnimationsModule
      ],
      providers: [
        FormBuilder,
        { provide: UserService, useValue: userServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EditUser);
    component = fixture.componentInstance;
    mockUserService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Setup default service responses
    mockUserService.getUserById.and.returnValue(of(mockGetUserResponse));
    mockUserService.updateUser.and.returnValue(of({ isSuccess: true, message: 'Updated', statusCode: 200 }));
    mockUserService.updateUserEmail.and.returnValue(of({ isSuccess: true, message: 'Email updated', statusCode: 200 }));
    mockUserService.verifyEmailChange.and.returnValue(of({ isSuccess: true, message: 'Verified', statusCode: 200 }));
    mockUserService.resetUserPassword.and.returnValue(of({ isSuccess: true, message: 'Password reset', statusCode: 200 }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user data on init', () => {
    fixture.detectChanges();
    
    expect(mockUserService.getUserById).toHaveBeenCalledWith(1);
    expect(component.userData()).toEqual(mockUserData);
    expect(component.profileForm.value.firstName).toBe('John');
    expect(component.profileForm.value.lastName).toBe('Doe');
  });

  it('should handle invalid user ID', () => {
    mockActivatedRoute.queryParams = of({ userId: null });
    
    fixture.detectChanges();
    
    expect(mockToastService.error).toHaveBeenCalledWith('Invalid user ID');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin-user']);
  });

  it('should handle user data load error', () => {
    mockUserService.getUserById.and.returnValue(throwError(() => new Error('Load failed')));
    
    fixture.detectChanges();
    
    expect(mockToastService.error).toHaveBeenCalledWith('Load failed');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin-user']);
  });

  it('should save profile successfully', () => {
    fixture.detectChanges();
    
    component.profileForm.patchValue({
      firstName: 'Jane',
      lastName: 'Smith',
      userName: 'janesmith',
      phoneNumber: '+**********',
      accountStatus: 'Active'
    });
    
    component.onSaveProfile();
    
    expect(mockUserService.updateUser).toHaveBeenCalledWith({
      userId: 1,
      firstName: 'Jane',
      lastName: 'Smith',
      userName: 'janesmith',
      phoneNumber: '+**********',
      accountStatus: 'Active'
    });
    expect(mockToastService.success).toHaveBeenCalledWith('User profile updated successfully');
  });

  it('should handle profile save error', () => {
    fixture.detectChanges();
    mockUserService.updateUser.and.returnValue(throwError(() => new Error('Update failed')));
    
    component.profileForm.patchValue({
      firstName: 'Jane',
      lastName: 'Smith',
      userName: 'janesmith',
      phoneNumber: '+**********',
      accountStatus: 'Active'
    });
    
    component.onSaveProfile();
    
    expect(mockToastService.error).toHaveBeenCalledWith('Update failed');
  });

  it('should not save invalid profile form', () => {
    fixture.detectChanges();
    
    component.profileForm.patchValue({
      firstName: '', // Invalid - required
      lastName: 'Smith',
      userName: 'janesmith',
      phoneNumber: '+**********',
      accountStatus: 'Active'
    });
    
    component.onSaveProfile();
    
    expect(mockUserService.updateUser).not.toHaveBeenCalled();
    expect(mockToastService.error).toHaveBeenCalledWith('Please fill in all required fields correctly');
  });

  it('should send verification code for email change', () => {
    fixture.detectChanges();
    
    component.emailForm.patchValue({
      email: '<EMAIL>'
    });
    
    component.onSendVerificationCode();
    
    expect(mockUserService.updateUserEmail).toHaveBeenCalledWith({
      userId: 1,
      newEmail: '<EMAIL>'
    });
    expect(component.emailVerificationRequired()).toBe(true);
    expect(mockToastService.success).toHaveBeenCalledWith('Verification code <NAME_EMAIL>');
  });

  it('should not send verification code for same email', () => {
    fixture.detectChanges();
    
    component.emailForm.patchValue({
      email: '<EMAIL>' // Same as original
    });
    
    component.onSendVerificationCode();
    
    expect(mockUserService.updateUserEmail).not.toHaveBeenCalled();
    expect(mockToastService.info).toHaveBeenCalledWith('Email address is the same as current email');
  });

  it('should verify email change successfully', () => {
    fixture.detectChanges();
    component.emailVerificationRequired.set(true);
    
    component.emailForm.patchValue({ email: '<EMAIL>' });
    component.verificationForm.patchValue({ verificationCode: '123456' });
    
    component.onVerifyAndUpdateEmail();
    
    expect(mockUserService.verifyEmailChange).toHaveBeenCalledWith({
      userId: 1,
      email: '<EMAIL>',
      verificationCode: '123456'
    });
    expect(mockToastService.success).toHaveBeenCalledWith('Email updated successfully');
    expect(component.emailVerificationRequired()).toBe(false);
  });

  it('should reset password with confirmation', () => {
    const mockConfirmToast = {
      onAction: () => of(true)
    };
    mockToastService.confirm.and.returnValue(mockConfirmToast as any);
    
    fixture.detectChanges();
    
    component.onResetPassword();
    
    expect(mockToastService.confirm).toHaveBeenCalledWith(
      'Reset Password',
      jasmine.stringContaining('Are you sure you want to reset the password for John Doe?'),
      'Reset Password',
      'CONFIRM'
    );
    expect(mockUserService.resetUserPassword).toHaveBeenCalledWith(1);
    expect(mockToastService.success).toHaveBeenCalledWith('Password reset successfully. Temporary password has been sent to the user.');
  });

  it('should navigate back to users', () => {
    component.onBackToUsers();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin-user']);
  });

  it('should cancel and navigate back', () => {
    component.onCancel();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin-user']);
  });

  it('should return correct user display name', () => {
    fixture.detectChanges();
    expect(component.getUserDisplayName()).toBe('John Doe');
  });

  it('should return default display name when no user data', () => {
    expect(component.getUserDisplayName()).toBe('User');
  });

  it('should validate form fields correctly', () => {
    fixture.detectChanges();
    
    // Test required field error
    component.profileForm.get('firstName')?.setValue('');
    component.profileForm.get('firstName')?.markAsTouched();
    expect(component.getFieldError(component.profileForm, 'firstName')).toBe('firstName is required');
    
    // Test email validation
    component.emailForm.get('email')?.setValue('invalid-email');
    component.emailForm.get('email')?.markAsTouched();
    expect(component.getFieldError(component.emailForm, 'email')).toBe('Please enter a valid email address');
    
    // Test minlength validation
    component.profileForm.get('firstName')?.setValue('A');
    component.profileForm.get('firstName')?.markAsTouched();
    expect(component.getFieldError(component.profileForm, 'firstName')).toBe('firstName must be at least 2 characters');
  });
});
