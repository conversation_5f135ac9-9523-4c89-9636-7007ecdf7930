.step-progress-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content;

  .progress-header {
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .steps-list {
    .step-item {
      display: flex;
      gap: 16px;
      position: relative;

      &:not(:last-child) {
        margin-bottom: 32px;
      }

      .step-icon-container {
        position: relative;
        flex-shrink: 0;

        .step-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          z-index: 2;

          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }

          &.completed {
            background-color: #4caf50;
            color: white;
          }

          &.current {
            background-color: var(--ivy-sky-blue, #2196f3);
            color: white;
          }

          &.pending {
            background-color: #f5f5f5;
            color: #999;
            border: 2px solid #e0e0e0;
          }
        }

        .step-connector {
          position: absolute;
          top: 40px;
          left: 50%;
          transform: translateX(-50%);
          width: 2px;
          height: 32px;
          background-color: #e0e0e0;
          z-index: 1;

          &.completed {
            background-color: #4caf50;
          }
        }
      }

      .step-content {
        flex: 1;
        padding-top: 4px;

        .step-title {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .step-description {
          margin: 0 0 8px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.4;
        }

        .current-step-label {
          display: inline-block;
          background-color: var(--ivy-sky-blue, #2196f3);
          color: white;
          font-size: 12px;
          font-weight: 500;
          padding: 4px 8px;
          border-radius: 12px;
        }
      }

      // Status-specific styling
      &.step-completed {
        .step-title {
          color: #4caf50;
        }
      }

      &.step-current {
        .step-title {
          color: var(--ivy-sky-blue, #2196f3);
        }
      }

      &.step-pending {
        .step-title {
          color: #999;
        }
        
        .step-description {
          color: #bbb;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .step-progress-container {
    padding: 16px;

    .steps-list {
      .step-item {
        gap: 12px;

        &:not(:last-child) {
          margin-bottom: 24px;
        }

        .step-icon-container {
          .step-icon {
            width: 32px;
            height: 32px;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .step-connector {
            top: 32px;
            height: 24px;
          }
        }

        .step-content {
          .step-title {
            font-size: 14px;
          }

          .step-description {
            font-size: 13px;
          }
        }
      }
    }
  }
}
