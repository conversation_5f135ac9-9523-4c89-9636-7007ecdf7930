import { Injectable } from '@angular/core';
import type { ApiBaseResponse } from '../models/ApiResponse';
import { GetStoresApiResponse, StoreQueryParams } from '../models/StoreModel';
import { BaseService, RequestOptions } from './base.service';

@Injectable({
  providedIn: 'root'
})
export class StoreService extends BaseService {

  async getAllStores(params: StoreQueryParams = {}): Promise<ApiBaseResponse<GetStoresApiResponse>> {
    const queryParams: { [key: string]: string | number | boolean } = {};

    if (params.pageIndex !== undefined) {
      queryParams['PageIndex'] = params.pageIndex;
    }
    if (params.pageSize !== undefined) {
      queryParams['PageSize'] = params.pageSize;
    }
    if (params.searchText) {
      queryParams['SearchText'] = params.searchText;
    }
    if (params.isPaginated !== undefined) {
      queryParams['IsPaginated'] = params.isPaginated;
    }
    if (params.sortColumn) {
      queryParams['SortColumn'] = params.sortColumn;
    }
    if (params.sortOrder) {
      queryParams['SortOrder'] = params.sortOrder;
    }

    const options: RequestOptions = {
      params: queryParams
    };

    return await this.get<GetStoresApiResponse>('Store/GetAllStores', options);
  }
}
