<div class="toast-container" [class]="'toast-' + data.type">
    <div class="toast-content">
    <div class="toast-icon">
        <mat-icon   class="material-symbols toast-icon-symbol">{{ getIcon() }}</mat-icon>
    </div>
    <div class="toast-message">
        {{ data.message }}
    </div>
    <div class="toast-actions">
        @if (data.action) {
        <button mat-button class="toast-action-button" (click)="onAction()">
            {{ data.action }}
        </button>
        }
        @if (data.showCloseButton) {
        <button mat-icon-button class="toast-close-button" (click)="onClose()">
            <mat-icon class="material-symbols" >close</mat-icon>
        </button>
        }
    </div>
    </div>
</div>