import { Injectable, inject } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarRef } from '@angular/material/snack-bar';
import { ToastConfirm } from '@components/toast-confirm/toast-confirm';
import { Toast } from '@components/toast/toast';
import { ToastConfig, ToastConfirmData, ToastData, ToastType } from '@models/ToastModel';

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private snackBar = inject(MatSnackBar);

  // Default configuration
  private defaultConfig: Partial<ToastConfig> = {
    duration: 5000,
    position: 'bottom',
    horizontalPosition: 'center',
    showCloseButton: true
  };

  /**
   * Show a toast message with custom configuration
   */
  show(config: ToastConfig): MatSnackBarRef<Toast> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    const data: ToastData = {
      message: finalConfig.message,
      type: finalConfig.type,
      action: finalConfig.action,
      showCloseButton: finalConfig.showCloseButton,
    };

    const snackBarConfig: MatSnackBarConfig = {
      data,
      duration: finalConfig.duration,
      verticalPosition: finalConfig.position,
      horizontalPosition: finalConfig.horizontalPosition,
      panelClass: [`toast-${finalConfig.type}-container`]
    };

    return this.snackBar.openFromComponent(Toast, snackBarConfig);
  }

  /**
   * Show a toast message with custom configuration
   */
  showConfirm(config: ToastConfig, data: ToastConfirmData): MatSnackBarRef<ToastConfirm> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    const snackBarConfig: MatSnackBarConfig = {
      data,
      duration: finalConfig.duration,
      verticalPosition: finalConfig.position,
      horizontalPosition: finalConfig.horizontalPosition,
      panelClass: [`toast-${finalConfig.type}-container`,'ivy-toast-confirm']
    };

    return this.snackBar.openFromComponent(ToastConfirm, snackBarConfig);
  }

  /**
   * Show a success toast message
   */
  success(message: string, action?: string, duration?: number): MatSnackBarRef<Toast> {
    return this.show({
      message,
      type: 'success',
      action,
      duration: duration || 2000,
      // position: 'top',
      // horizontalPosition: 'right'
    });
  }

  /**
   * Show an error toast message
   */
  error(message: string, action?: string, duration?: number): MatSnackBarRef<Toast> {
    return this.show({
      message,
      type: 'error',
      action,
      duration: duration || 6000
    });
  }

  /**
   * Show a warning toast message
   */
  warning(message: string, action?: string, duration?: number): MatSnackBarRef<Toast> {
    return this.show({
      message,
      type: 'warning',
      action,
      duration: duration || 5000
    });
  }

  /**
   * Show an info toast message
   */
  info(message: string, action?: string, duration?: number): MatSnackBarRef<Toast> {
    return this.show({
      message,
      type: 'info',
      action,
      duration: duration || 4000
    });
  }

  /**
   * Show a simple message toast (info type by default)
   */
  message(message: string, duration?: number): MatSnackBarRef<Toast> {
    return this.info(message, undefined, duration);
  }

  /**
   * Dismiss all active toasts
   */
  dismissAll(): void {
    this.snackBar.dismiss();
  }

  /**
   * Update default configuration
   */
  updateDefaultConfig(config: Partial<ToastConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * Get current default configuration
   */
  getDefaultConfig(): Partial<ToastConfig> {
    return { ...this.defaultConfig };
  }

  /**
   * Show API response message based on success/error
   */
  showApiResponse(response: { isSuccess: boolean; message: string }, successAction?: string, errorAction?: string): MatSnackBarRef<Toast> {
    if (response.isSuccess) {
      return this.success(response.message, successAction);
    } else {
      return this.error(response.message, errorAction);
    }
  }

  /**
   * Show loading toast (long duration, no auto-dismiss)
   */
  loading(message: string = 'Loading...'): MatSnackBarRef<Toast> {
    return this.show({
      message,
      type: 'info',
      duration: 0, // No auto-dismiss
      showCloseButton: false
    });
  }

  /**
   * Show confirmation toast with action
   */
  confirm(title:string,message: string, actionText: string = 'Confirm',confirmationType:'CONFIRM' | 'DELETE'='CONFIRM', duration?: number): MatSnackBarRef<ToastConfirm> {
    const data: ToastConfirmData = {
      title:title,
      message: message,
      confirmationType: confirmationType,
      type: "warning",
      action: actionText,
      showCloseButton: true,
    };
    return this.showConfirm({
      message,
      type: 'warning',
      action: actionText,
      duration: 80000000000000000,
      showCloseButton: true,
      position: 'top',
      horizontalPosition: 'center'
    },data);
  }
}
