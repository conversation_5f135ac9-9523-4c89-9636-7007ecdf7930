<div class="edit-user-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <button mat-icon-button (click)="onBackToUsers()" class="back-button">
        <mat-icon class="material-symbols">arrow_back</mat-icon>
      </button>
      <h1>Edit User {{ getUserDisplayName() }}</h1>
    </div> 
  </div>

  <!-- Loading Spinner -->
  @if (isLoading()) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading user data...</p>
    </div>
  }
  @else
  {
  <!-- Main Content --> 
    <div class="content-container bg-ivy-base-white rounded-xl ">
      <!-- Tab Navigation -->
      <mat-tab-group class="user-tabs" animationDuration="300ms">
        
        <!-- Profile Tab -->
        <mat-tab>
          <ng-template mat-tab-label>
            <div class="flex gap-2 justify-center items-center ">
            <mat-icon class="material-symbols">person</mat-icon>
            <span>Profile</span>
            </div>
          </ng-template>
          
          <div class="tab-content">
            <mat-card class="profile-card !bg-ivy-base-white">
              <mat-card-header>
                <mat-card-title class="font-bold">User Information</mat-card-title>
                <mat-card-subtitle class="!text-sm">Editing User: {{ getUserDisplayName() }}</mat-card-subtitle>
              </mat-card-header>
              
              <mat-card-content  class="mt-8">
                <form [formGroup]="profileForm" class="profile-form">
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>First Name</mat-label>
                      <input matInput formControlName="firstName" placeholder="Enter first name">
                      @if (getFieldError(profileForm, 'firstName')) {
                        <mat-error>{{ getFieldError(profileForm, 'firstName') }}</mat-error>
                      }
                    </mat-form-field>
                    
                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>Last Name</mat-label>
                      <input matInput formControlName="lastName" placeholder="Enter last name">
                      @if (getFieldError(profileForm, 'lastName')) {
                        <mat-error>{{ getFieldError(profileForm, 'lastName') }}</mat-error>
                      }
                    </mat-form-field>
                  </div>
                  
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>User Name</mat-label>
                      <input matInput formControlName="userName" placeholder="Enter username">
                      @if (getFieldError(profileForm, 'userName')) {
                        <mat-error>{{ getFieldError(profileForm, 'userName') }}</mat-error>
                      }
                    </mat-form-field>
                    
                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>Phone Number</mat-label>
                      <input matInput formControlName="phoneNumber" placeholder="Enter phone number">
                      @if (getFieldError(profileForm, 'phoneNumber')) {
                        <mat-error>{{ getFieldError(profileForm, 'phoneNumber') }}</mat-error>
                      }
                    </mat-form-field>
                  </div>  
                </form>
              </mat-card-content>
              
              <mat-card-actions class="card-actions flex !justify-between">
                <div class="flex gap-2 flex-col">
                 <div class="metadata-row">
                  <span class="metadata-label">Created On:</span>
                  <span class="metadata-value">{{ userData()?.createdOn | date:'medium' }}</span>
                </div>
                <div class="metadata-row">
                  <span class="metadata-label">Last Logged In:</span>
                  <span class="metadata-value">
                    @if (userData()?.lastLoggedIn) {
                      {{ userData()?.lastLoggedIn | date:'medium' }}
                    } @else {
                      Never
                    }
                  </span>
                </div>
                </div>
                <div  class="flex gap-2"> 

                  <app-primary-button 
                    mat-raised-button 
                    [type]="'button'"
                    (buttonClick)="onSaveProfile()"
                    [loading] = "isLoading()"
                    [disabled]="isLoading() || profileForm.invalid"
                    [text]="'Update'"
                  />   
                </div>
              </mat-card-actions>
            </mat-card> 
          </div>
        </mat-tab>
        
        <!-- Email Setting Tab -->
        <mat-tab>
          <ng-template mat-tab-label>
            <div class="flex gap-2 justify-center items-center">

            <mat-icon class="material-symbols">email</mat-icon>
            <span>Email Setting</span>
            @if (emailVerificationRequired()) {
              <span class="verification-badge">(verification pending)</span>
            }
            </div>  
          </ng-template>
          
          <div class="tab-content">
            <mat-card class="email-card  !bg-ivy-base-white">
              <mat-card-header>
                <mat-card-title>Email Settings</mat-card-title>
              </mat-card-header>
              
              <mat-card-content class="mt-4">
                <form [formGroup]="emailForm" class="email-form">
                  <div class="email-section">
                    <mat-form-field appearance="outline" class="email-field">
                      <mat-label>Email Address</mat-label>
                      <input matInput formControlName="email" placeholder="Enter email address" type="email">
                      @if (getFieldError(emailForm, 'email')) {
                        <mat-error>{{ getFieldError(emailForm, 'email') }}</mat-error>
                      }
                    </mat-form-field>
                    
                    
                        <app-primary-button 
                          mat-raised-button 
                          [type]="'button'"
                          (buttonClick)="onSendVerificationCode()"
                          [loading] = "isLoading()"
                          [disabled]="isLoading() || emailForm.invalid"
                          [text]="'Send Verification Code'"
                        />   
                  </div>
                </form>
                
                <!-- Email Verification Section -->
                @if (emailVerificationRequired()) {
                  <div class="verification-section">
                    <div class="verification-alert">
                      <mat-icon class="warning-icon material-symbols">warning</mat-icon>
                      <div class="alert-content">
                        <h4>Email Verification Required</h4>
                        <p>We've sent a verification code to <strong>{{ emailForm.value.email }}</strong>. Please enter the code below to confirm the email change.</p>
                      </div>
                    </div>
                    
                    <form [formGroup]="verificationForm" class="verification-form">
                      <div class="verification-input-section">
                        <mat-form-field appearance="outline" class="verification-field">
                          <mat-label>Verification Code</mat-label>
                          <input matInput formControlName="verificationCode" placeholder="Enter 6-digit code" maxlength="6">
                          @if (getFieldError(verificationForm, 'verificationCode')) {
                            <mat-error>{{ getFieldError(verificationForm, 'verificationCode') }}</mat-error>
                          }
                        </mat-form-field>
                        
                        <app-primary-button 
                          mat-raised-button 
                          [type]="'button'"
                          (buttonClick)="onVerifyAndUpdateEmail()"
                          [loading] = "isLoading()"
                          [disabled]="isLoading() || verificationForm.invalid"
                          [text]="'Verify and Update Email'"
                        />   
                      </div>
                      
                      <button mat-button type="button" (click)="onSendVerificationCode()" [disabled]="isLoading()">
                        Resend Code
                      </button>
                    </form>
                  </div>
                }
                
                <!-- Email Change Policy -->
                <div class="policy-section">
                  <mat-icon class="info-icon material-symbols">info</mat-icon>
                  <div class="policy-content">
                    <h4>Email Change Policy</h4>
                    <p>When you change the email address, the user will need to verify the new email before it becomes active. The old email will remain active until verification is complete.</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
         
        
      </mat-tab-group>
    </div> 
  }
</div>
