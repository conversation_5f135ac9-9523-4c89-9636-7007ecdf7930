.mfa-setup-modal {
  padding: 24px;
  max-width: 480px;
  width: 100%;

}


.modal-header {
  text-align: center;
  margin-bottom: 24px;

  .icon-container {
    .security-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: var(--ivy-primary-600, #2563eb);
    }
  }

  h2 {
    margin: 0 0 12px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ivy-neutral-800, #1f2937);
  }

  .modal-subtitle {
    margin: 0;
    font-size: 0.875rem;
    color: var(--ivy-neutral-600, #6b7280);
    line-height: 1.5;
  }
}

.modal-content {
  margin-bottom: 32px;

  .benefits-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .benefit-item {
      display: flex;
      align-items: center;
      gap: 12px;

      .benefit-icon {
        color: var(--ivy-success-600, #059669);
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      span {
        font-size: 0.875rem;
        color: var(--ivy-neutral-700, #374151);
      }
    }
  }
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin: 0;
  padding: 0;

  .skip-button {
    color: var(--ivy-neutral-600, #6b7280);
  }

  .setup-button {
    background-color: var(--ivy-primary-600, #2563eb);
    color: white;

    &:hover {
      background-color: var(--ivy-primary-700, #1d4ed8);
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .mfa-setup-modal {
    padding: 20px;
  }

  .modal-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      width: 100%;
    }
  }
}