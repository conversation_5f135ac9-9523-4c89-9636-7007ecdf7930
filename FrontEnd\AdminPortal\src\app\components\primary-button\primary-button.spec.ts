import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PrimaryButton } from './primary-button';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('PrimaryButton', () => {
  let component: PrimaryButton;
  let fixture: ComponentFixture<PrimaryButton>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PrimaryButton, NoopAnimationsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PrimaryButton);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }); 

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit click event when clicked', () => {
    let clickEmitted = false;
    component.buttonClick.subscribe(() => clickEmitted = true);

    const buttonElement = fixture.nativeElement.querySelector('button');
    buttonElement.click();

    expect(clickEmitted).toBeTrue();
  });

  it('should not emit click event when disabled', () => {
    let clickEmitted = false;
    component.buttonClick.subscribe(() => clickEmitted = true);

    fixture.componentRef.setInput('disabled', true);
    fixture.detectChanges();

    const buttonElement = fixture.nativeElement.querySelector('button');
    buttonElement.click();

    expect(clickEmitted).toBeFalse();
  });

  it('should show spinner when loading', () => {
    fixture.componentRef.setInput('loading', true);
    fixture.detectChanges();

    const spinnerElement = fixture.nativeElement.querySelector('mat-spinner');
    expect(spinnerElement).toBeTruthy();
  });

  it('should compute CSS classes correctly', () => {
    fixture.componentRef.setInput('buttonClasses', 'custom-class');
    fixture.componentRef.setInput('size', 'large');
    fixture.detectChanges();

    expect(component.cssClasses()).toContain('primary-button');
    expect(component.cssClasses()).toContain('custom-class');
    expect(component.cssClasses()).toContain('large');
  });

});
