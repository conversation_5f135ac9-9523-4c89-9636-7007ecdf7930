  .content-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 24px;
    align-items: start;

    .progress-sidebar {
      position: sticky;
      top: 24px;
    }

    .main-content {
      .form-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        .mat-mdc-card-header {
          padding: 24px 24px 16px 24px;

          .mat-mdc-card-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .mat-mdc-card-subtitle {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
          }

          // Verification header styling
          &.verification-header {
            text-align: center;
            padding: 32px 24px 16px 24px;

            .verification-icon {
              width: 64px;
              height: 64px;
              border-radius: 50%;
              background-color: var(--ivy-sky-blue, #2196f3);
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 16px auto;

              mat-icon {
                font-size: 32px;
                width: 32px;
                height: 32px;
                color: white;
              }
            }
          }

          // MFA header styling
          &.mfa-header {
            text-align: center;
            padding: 32px 24px 16px 24px;

            .mfa-icon {
              width: 64px;
              height: 64px;
              border-radius: 50%;
              background-color: #4caf50;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 16px auto;

              mat-icon {
                font-size: 32px;
                width: 32px;
                height: 32px;
                color: white;
              }
            }
          }
        }

        .mat-mdc-card-content {
          padding: 0 24px 24px 24px;

          .user-form {
            .form-row {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 16px;
              margin-bottom: 20px;

              .form-field {
                &.full-width {
                  grid-column: 1 / -1;
                }
              }
            }

            .error-message {
              display: flex;
              align-items: center;
              gap: 8px;
              color: #f44336;
              font-size: 14px;
              margin-top: -12px;
              margin-bottom: 16px;

              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;
              }
            }
          }

          // Verification content styling
          &.verification-content {
            text-align: center;
            padding: 16px 24px 24px 24px;

            .verification-text {
              font-size: 16px;
              color: #666;
              line-height: 1.5;
              margin-bottom: 32px;

              strong {
                color: var(--ivy-sky-blue, #2196f3);
                font-weight: 600;
              }
            }

            .verification-form {
              max-width: 300px;
              margin: 0 auto;

              .verification-input {
                margin-bottom: 16px;
                
                ::ng-deep .mat-mdc-form-field-input-control input {
                  text-align: center;
                  font-size: 18px;
                  font-weight: 600;
                  letter-spacing: 2px;
                }
              }

              .resend-button {
                color: var(--ivy-sky-blue, #2196f3);
                font-weight: 500;
                
                &:hover {
                  background-color: rgba(33, 150, 243, 0.08);
                }
              }
            }
          }

          // MFA content styling
          &.mfa-content {
            text-align: center;
            padding: 16px 24px 24px 24px;

            .mfa-text {
              font-size: 16px;
              color: #666;
              line-height: 1.5;
              margin-bottom: 32px;
            }

            .mfa-options {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 16px;
              max-width: 500px;
              margin: 0 auto;

              .mfa-option {
                padding: 20px;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                transition: all 0.2s ease;

                &:hover {
                  border-color: var(--ivy-sky-blue, #2196f3);
                  background-color: rgba(33, 150, 243, 0.04);
                }

                mat-icon {
                  font-size: 32px;
                  width: 32px;
                  height: 32px;
                  color: var(--ivy-sky-blue, #2196f3);
                  margin-bottom: 12px;
                }

                .option-content {
                  h4 {
                    margin: 0 0 8px 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                  }

                  p {
                    margin: 0;
                    font-size: 14px;
                    color: #666;
                    line-height: 1.4;
                  }
                }
              }
            }
          }
        }

        .mat-mdc-card-actions {
          padding: 16px 24px 24px 24px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 16px;

          &.form-actions {
            border-top: 1px solid #e0e0e0;
            margin-top: 8px;
            padding-top: 24px;

            button[mat-button] {
              color: #666;
              font-weight: 500;

              &:hover {
                background-color: rgba(0, 0, 0, 0.04);
              }
            }
          }
        }
      }
    }
  } 