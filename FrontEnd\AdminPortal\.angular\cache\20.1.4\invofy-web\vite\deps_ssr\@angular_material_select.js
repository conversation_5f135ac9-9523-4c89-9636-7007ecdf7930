import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatOptgroup,
  MatOption,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger
} from "./chunk-BNDYX4UE.js";
import "./chunk-KUFYZA3I.js";
import "./chunk-MGEP7BAQ.js";
import "./chunk-HDRTJDGT.js";
import "./chunk-PCRFCOUK.js";
import "./chunk-XTABFOM5.js";
import "./chunk-Q7WDE6RN.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>fi<PERSON>,
  MatSuffix
} from "./chunk-ICHJSQL5.js";
import "./chunk-N25GJ5OQ.js";
import "./chunk-LECRHCY3.js";
import "./chunk-CQS2UK3I.js";
import "./chunk-FJSIZZGE.js";
import "./chunk-YRVB5RKG.js";
import "./chunk-35H5YHTD.js";
import "./chunk-3K6B5E4U.js";
import "./chunk-QMU5B7SP.js";
import "./chunk-XLF3NYPG.js";
import "./chunk-RLKNDOON.js";
import "./chunk-EXY2VFEP.js";
import "./chunk-KXD77YIU.js";
import "./chunk-YZRXDCC3.js";
import "./chunk-KGZSM6MR.js";
import "./chunk-5AU7KZXI.js";
import "./chunk-FQ7NNTHO.js";
import "./chunk-53SREPDD.js";
import "./chunk-4XXXTM2K.js";
import "./chunk-PPO2RDEN.js";
import "./chunk-WCSRJUZM.js";
import {
  require_operators
} from "./chunk-2XLRDDJW.js";
import {
  require_cjs
} from "./chunk-43KPLV43.js";
import "./chunk-TXGYY7YM.js";
import {
  __toESM
} from "./chunk-6DU2HRTW.js";

// node_modules/@angular/material/fesm2022/select.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
var matSelectAnimations = {
  // Represents
  // trigger('transformPanel', [
  //   state(
  //     'void',
  //     style({
  //       opacity: 0,
  //       transform: 'scale(1, 0.8)',
  //     }),
  //   ),
  //   transition(
  //     'void => showing',
  //     animate(
  //       '120ms cubic-bezier(0, 0, 0.2, 1)',
  //       style({
  //         opacity: 1,
  //         transform: 'scale(1, 1)',
  //       }),
  //     ),
  //   ),
  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),
  // ])
  /** This animation transforms the select's overlay panel on and off the page. */
  transformPanel: {
    type: 7,
    name: "transformPanel",
    definitions: [
      {
        type: 0,
        name: "void",
        styles: {
          type: 6,
          styles: { opacity: 0, transform: "scale(1, 0.8)" },
          offset: null
        }
      },
      {
        type: 1,
        expr: "void => showing",
        animation: {
          type: 4,
          styles: {
            type: 6,
            styles: { opacity: 1, transform: "scale(1, 1)" },
            offset: null
          },
          timings: "120ms cubic-bezier(0, 0, 0.2, 1)"
        },
        options: null
      },
      {
        type: 1,
        expr: "* => void",
        animation: {
          type: 4,
          styles: { type: 6, styles: { opacity: 0 }, offset: null },
          timings: "100ms linear"
        },
        options: null
      }
    ],
    options: {}
  }
};
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
