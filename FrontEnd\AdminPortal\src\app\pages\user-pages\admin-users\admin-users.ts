import { AfterViewInit, Component, ViewChild, signal, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

import { User, UserQueryParams } from '@models/UserModel';
import { UserService } from '@services/user.service';
import { ToastService } from '@services/toast.service';
import { UpdateMfaStatusRequest } from '@models/user-model/CreateUserModel';
import { TableSearchInput } from '@components/table-components/table-search-input/table-search-input';
import { UserActionsModal, UserActionsData, UserActionResult } from '@components/user-actions-modal/user-actions-modal';
import { ResetPasswordModal, ResetPasswordModalData } from '@components/reset-password-modal/reset-password-modal';
import { DatePipe } from '@angular/common';
@Component({
  selector: 'app-admin-users',
  imports: [
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatSelectModule,
    MatDividerModule,
    FormsModule,
    TableSearchInput
  ],
  providers: [DatePipe],
  templateUrl: './admin-users.html',
  styleUrl: './admin-users.scss'
})
export class AdminUsers implements OnInit, AfterViewInit, OnDestroy {
  displayedColumns: string[] = ['name', 'email', 'status', 'mfa', 'createdDate', 'lastLogin', 'actions'];
  dataSource = new MatTableDataSource<User>([]);
  @ViewChild(MatPaginator, { static: false }) paginator?: MatPaginator;

  isLoading = signal<boolean>(false);
  errorMessage = signal<string>('');
  searchText = signal<string>('');
  sortColumn = signal<string>('');
  sortOrder = signal<string>('');
  totalRecords = signal<number>(0);

  pageSize = signal<number>(10);
  pageIndex = signal<number>(0);
  pageSizeOptions = [5, 10, 25, 50];

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private userService: UserService,
    private toastService: ToastService,
    private router: Router,
    private dialog: MatDialog,
    private datePipe: DatePipe
  ) { }

  ngOnInit(): void {
    this.setupSearch();
  }

  ngAfterViewInit(): void {

    setTimeout(() => {
      this.loadUsers();
    }, 0);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchText => {
      this.searchText.set(searchText);
      this.pageIndex.set(0);
      this.loadUsers();
    });
  }

 async loadUsers() {
    this.isLoading.set(true);
    this.errorMessage.set('');


    const params: UserQueryParams = {
      PageIndex: this.pageIndex() + 1,
      PageSize: this.pageSize(),
      SearchText: this.searchText() || undefined,
      SortColumn: this.sortColumn() || undefined,
      SortOrder: this.sortOrder() || undefined,
      IsPaginated: true,
      SafePageIndex: this.pageIndex() + 1,
      SafePageSize: this.pageSize()
    };

    const  response= await this.userService.getAllUsers(params);
        if (response.isSuccess) {
          this.dataSource.data = response.data?.users || [];
          this.totalRecords.set(response.data?.pagination?.totalRecords || 0);
        } else {
          this.errorMessage.set(response.message || 'Failed to load users');
          this.dataSource.data = [];
          this.totalRecords.set(0);
        }
        this.isLoading.set(false);
      
  } 
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchSubject.next(target.value);
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex.set(event.pageIndex);
    this.pageSize.set(event.pageSize);
    this.loadUsers();
  }

  onRefresh(): void {
    this.loadUsers();
  }

  

  onEditUser(user: User): void {
    // Navigate to edit user page with userCode as query parameter
    this.router.navigate(['/admin-user/edit'], {
      queryParams: {
        userCode: user.userCode || undefined,
        userId: !user.userCode ? user.userId : undefined
      }
    });
  }

  onDeleteUser(user: User): void {
    // Show user actions modal first
    const dialogRef = this.dialog.open(UserActionsModal, {
      width: '480px',
      maxWidth: '90vw',
      data: { user } as UserActionsData,
      panelClass: 'user-actions-dialog',
      disableClose: false
    });

    dialogRef.afterClosed().subscribe((result: UserActionResult) => {
      if (result) {
        switch (result.action) {
          case 'mark-inactive':
            this.onMarkUserInactive(result.user);
            break;
          case 'delete-permanently':
            this.onDeleteUserPermanently(result.user);
            break;
          case 'cancel':
          default:
            // Do nothing
            break;
        }
      }
    });
  }

  private onMarkUserInactive(user: User): void {
    // Show confirmation for marking as inactive
    const confirmToast = this.toastService.confirm(
      'Mark User as Inactive',
      `Are you sure you want to mark ${user.name} as inactive? They will not be able to access the system.`,
      'Mark Inactive',
      'CONFIRM'
    );

    confirmToast.onAction().subscribe(async () => {
      try {
        // Use userCode if available, otherwise use userId as string
        const userIdentifier = user.userCode || user.userId.toString();
        const response = await this.userService.deactivateUser(userIdentifier);

        if (response.isSuccess) {
          this.toastService.success(`${user.name} has been marked as inactive.`);
          this.loadUsers();
        } else {
          this.toastService.error(response.message || 'Failed to mark user as inactive. Please try again.');
        }
      } catch (error) {
        console.error('Error marking user as inactive:', error);
        this.toastService.error('An error occurred while marking the user as inactive. Please try again.');
      }
    });
  }

  private onDeleteUserPermanently(user: User): void {
    // Show confirmation for permanent deletion
    const confirmToast = this.toastService.confirm(
      'Delete User Permanently',
      `Are you sure you want to permanently delete ${user.name}? This action cannot be undone and will remove all user data.`,
      'Delete Permanently',
      'DELETE'
    );

    confirmToast.onAction().subscribe(async () => {
      // Call the delete API
      const response = await this.userService.deleteUser(user.userId);
          if (response.isSuccess) {
            this.toastService.success(`${user.name} has been deleted permanently.`);
            // Refresh the user list after successful deletion
            this.loadUsers();
          } else {
            this.toastService.error(response.message || 'Failed to delete user.');
          }
        
    });
  }

  onAddUser(): void {
    this.router.navigate(['/admin-user/add']);
  }

  onResetPassword(user: User): void {
    const dialogData: ResetPasswordModalData = {
      user: user
    };

    const dialogRef = this.dialog.open(ResetPasswordModal, {
      width: '500px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.success) {
        // Optionally refresh the user list or update user status
        this.loadUsers();
      }
    });
  }

  onDisableMfa(user: User): void {
    const confirmToast = this.toastService.confirm(
      `Disable MFA for ${user.name}? This will reduce account security.`,
      'Disable'
    );

    confirmToast.onAction().subscribe(async () => {
      const updateMfaRequest: UpdateMfaStatusRequest = {
        userId: user.userId,
        isEnabled: false
      };

      const response=await this.userService.updateMfaStatus(updateMfaRequest);
        
          if (response.isSuccess) {
            this.toastService.success(response.message || `MFA disabled for ${user.name}.`);
            this.loadUsers(); // Refresh the user list to update MFA status
          } else {
            this.toastService.error(response.message || 'Failed to disable MFA. Please try again.');
          } 
      
    });
  }

  onEnableMfa(user: User): void {
    const confirmToast = this.toastService.confirm(
      `Enable MFA for ${user.name}? This will enhance account security.`,
      'Enable'
    );

    confirmToast.onAction().subscribe(async () => {
      const updateMfaRequest: UpdateMfaStatusRequest = {
        userId: user.userId,
        isEnabled: true
      };

      const response=await this.userService.updateMfaStatus(updateMfaRequest)
      if (response.isSuccess) {
        this.toastService.success(response.message || `MFA enabled for ${user.name}.`);
        this.loadUsers(); // Refresh the user list to update MFA status
      } else {
        this.toastService.error(response.message || 'Failed to enable MFA. Please try again.');
      }
        
    });
  }

  onDeactivateUser(user: User): void {
    // Show confirmation toast before deactivating
    const confirmToast = this.toastService.confirm(
      `Deactivate ${user.name}? They will not be able to access the system.`,
      'Deactivate'
    );

    confirmToast.onAction().subscribe(async () => {
      try {
        // Use userCode if available, otherwise use userId as string
        const userIdentifier = user.userCode || user.userId.toString();
        const response = await this.userService.deactivateUser(userIdentifier);

        if (response.isSuccess) {
          this.toastService.success(`${user.name} has been deactivated.`);
          // Refresh the user list to update status
          this.loadUsers();
        } else {
          this.toastService.error(response.message || 'Failed to deactivate user. Please try again.');
        }
      } catch (error) {
        console.error('Error deactivating user:', error);
        this.toastService.error('An error occurred while deactivating the user. Please try again.');
      }
    });
  }

  onActivateUser(user: User): void {
    // Show confirmation toast before activating
    const confirmToast = this.toastService.confirm(
      `Activate ${user.name}? They will be able to access the system.`,
      'Activate'
    );

    confirmToast.onAction().subscribe(async () => {
      try {
        // Use userCode if available, otherwise use userId as string
        const userIdentifier = user.userCode || user.userId.toString();
        const response = await this.userService.activateUser(userIdentifier);

        if (response.isSuccess) {
          this.toastService.success(`${user.name} has been activated.`);
          // Refresh the user list to update status
          this.loadUsers();
        } else {
          this.toastService.error(response.message || 'Failed to activate user. Please try again.');
        }
      } catch (error) {
        console.error('Error activating user:', error);
        this.toastService.error('An error occurred while activating the user. Please try again.');
      }
    });
  }


  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'status-active';
      case 'inactive':
        return 'status-inactive';
      case 'pending':
        return 'status-pending';
      case 'deactivated':
        return 'status-deactivated';
      case 'locked':
        return 'status-locked';
      default:
        return 'status-deactivated';
    }
  }

  getMfaIconClass(mfa: string): string {
    switch (mfa.toLowerCase()) {
      case 'enabled':
        return 'mfa-enabled';
      case 'disabled':
        return 'mfa-disabled';
      default:
        return 'mfa-default';
    }
  }
   formatDate(dateString: string): string { 
    if (!dateString || dateString === 'Never') return 'Never';
    try {
      const date=new Date(dateString);
      return this.datePipe.transform(date, 'dd/MM/yyyy HH:mm z') || 'Never'; 
    } catch {
      return dateString;
    }
  }



}

