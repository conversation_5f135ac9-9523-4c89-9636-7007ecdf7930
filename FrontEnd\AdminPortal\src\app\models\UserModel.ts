export interface User {
  userId: number;
  userCode?: string; // Optional GUID for API calls
  name: string;
  email: string;
  status: string;
  mfa: string;
  createdDate: string;
  lastLogin: string;
}

export interface UserPagination {
  pageIndex: number | null;
  pageSize: number | null;
  totalRecords: number;
  totalPages: number | null;
  hasNextPage: boolean | null;
  hasPreviousPage: boolean | null;
  searchText: string | null;
  isPaginated: boolean;
  sortColumn: string | null;
  sortOrder: string;
}

export interface GetUsersApiResponse { 
    users: User[];
    pagination: UserPagination; 
}

export interface UserQueryParams {
  PageIndex?: number;
  PageSize?: number;
  SearchText?: string;
  SortColumn?: string;
  SortOrder?: string;
  IsPaginated?: boolean;
  SafePageIndex?: number;
  SafePageSize?: number;
}
