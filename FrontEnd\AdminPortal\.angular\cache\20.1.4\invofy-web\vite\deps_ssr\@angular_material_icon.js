import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  ICON_REGISTRY_PROVIDER,
  ICON_REGISTRY_PROVIDER_FACTORY,
  MAT_ICON_DEFAULT_OPTIONS,
  MAT_ICON_LOCATION,
  MAT_ICON_LOCATION_FACTORY,
  MatIcon,
  MatIconModule,
  MatIconRegistry,
  getMatIconFailedToSanitizeLiteralError,
  getMatIconFailedToSanitizeUrlError,
  getMatIconNameNotFoundError,
  getMatIconNoHttpProviderError
} from "./chunk-NEFYF3EI.js";
import "./chunk-OO4HW6GD.js";
import "./chunk-XG3USEXK.js";
import "./chunk-KGZSM6MR.js";
import "./chunk-5AU7KZXI.js";
import "./chunk-FQ7NNTHO.js";
import "./chunk-53SREPDD.js";
import "./chunk-4XXXTM2K.js";
import "./chunk-PPO2RDEN.js";
import "./chunk-WCSRJUZM.js";
import "./chunk-2XLRDDJW.js";
import "./chunk-43KPLV43.js";
import "./chunk-TXGYY7YM.js";
import "./chunk-6DU2HRTW.js";
export {
  ICON_REGISTRY_PROVIDER,
  ICON_REGISTRY_PROVIDER_FACTORY,
  MAT_ICON_DEFAULT_OPTIONS,
  MAT_ICON_LOCATION,
  MAT_ICON_LOCATION_FACTORY,
  MatIcon,
  MatIconModule,
  MatIconRegistry,
  getMatIconFailedToSanitizeLiteralError,
  getMatIconFailedToSanitizeUrlError,
  getMatIconNameNotFoundError,
  getMatIconNoHttpProviderError
};
