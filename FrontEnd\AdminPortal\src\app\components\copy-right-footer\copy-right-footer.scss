.copyright-footer {
  background: transparent;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.copyright-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 400;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 400;
  transition: color 0.2s ease;

  &:hover {
    color: rgba(255, 255, 255, 1);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
    border-radius: 4px;
  }
}

.separator {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

// Responsive Design
@media (max-width: 480px) {
  .footer-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .copyright-text,
  .footer-link {
    font-size: 0.8125rem;
  }
}