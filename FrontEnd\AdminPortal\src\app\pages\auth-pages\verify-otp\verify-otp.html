<app-auth-card 
    [title]="'Verify OTP'"
    [subtitle]="'Enter the verification code sent to your email'"
    class="max-w-full"
    > 
    <div class="flex flex-col gap-4">
        @if (errorMessage()) {
          <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {{ errorMessage() }}
          </div>
        }
        @if (successMessage()) {
          <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm">
            {{ successMessage() }}
          </div>
        }
        
        <!-- Email Display -->
        <div class="text-sm text-gray-600 text-center">
          Verification code sent to: <strong>{{ email() }}</strong>
        </div>
        
        <!-- OTP Input -->
        <app-form-input
          [label]="'Verification Code'"
          [placeholder]="'Enter 6-digit code'"
          [type]="'text'"
          [name]="'otp'"
          [iconName]="'lock'"
          [value]="otp()"
          [required]="true"
          [autocomplete]="'one-time-code'"
          (valueChange)="onOtpChange($event)">
        </app-form-input>
        
        <!-- Submit Button -->
        <app-primary-button
            [text]="'Verify Code'"
            [type]="'button'"
            [loading]="isLoading()"
            [disabled]="!isValidForm()"
            (buttonClick)="onSubmit()">
        </app-primary-button>
        
        <!-- Resend OTP -->
        <div class="text-center">
          <button
            type="button"
            class="resend-button"
            [class.disabled]="isResendDisabled()"
            (click)="onResendOtp()"
            [disabled]="isResendDisabled()">
            {{ resendButtonText() }}
          </button>
        </div>
        
        <!-- Back to Forgot Password -->
        <div class="text-center mt-4">
          <a href="#" class="text-sm font-medium text-ivy-sky-blue no-underline hover:text-ivy-sky-blue-dark hover:underline" 
            (click)="onBackToForgotPassword()">
            Back to Forgot Password
          </a>
        </div>
    </div>
</app-auth-card>
