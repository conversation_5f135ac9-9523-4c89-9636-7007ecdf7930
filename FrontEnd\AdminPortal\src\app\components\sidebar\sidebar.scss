
.sidebar{
    background-color: var(--ivy-dark-blue   );
    height: 100%;
    .sidebar-header{
        padding: 11px; 
        height: var(--header-height);
        display: flex; 
        align-items: center;
        img{
            width: var(--sidebar-logo-width);
            height: auto;
        }
        img.collapsed-logo{
            width: var(--sidebar-logo-width-collapsed); 
        }
    }
    .sidebar-content{
        padding: 11px;
        display: flex;
        flex-direction: column;
    }
    .sidebar-footer{
        padding-left: 11px;
        padding-right: 11px;
    }
     .mdc-list-item__primary-text,  .mdc-list-item__start{
        color: var(--ivy-base-white);
    }
    .mdc-list-item:hover::before {
        background-color: var(--ivy-base-white);
    }
    .sidebar-link-active {
        background-color: var(--ivy-base-white);
    }
    .sidebar-link-active .mdc-list-item__primary-text, .sidebar-link-active .mdc-list-item__start{
        color: var(--ivy-dark-blue);
    }
    .sidebar-toggale-button{
        padding: 9px 10px 4px;
    border-radius: 50%;
    color: white; 
    cursor: pointer;
        &:hover{
            background-color: var(--ivy-button-hover);
            
        }
    }
}