<app-auth-card 
    [title]="'Forgot Password'"
    [subtitle]="'Enter your email to reset your password.'"
    class="max-w-full"
    > 
    <div class="flex flex-col gap-4 "  >
        @if (errorMessage()) {
          <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {{ errorMessage() }}
          </div>
        }
        @if (successMessage()) {
          <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm">
            {{ successMessage() }}
          </div>
        }
        <app-form-input
      [label]="'Email'"
      [placeholder]="'<EMAIL>'"
      [type]="'email'"
      [name]="'email'"
      [iconName]="'email'"
      [value]="email()"
      [required]="true"
      [autocomplete]="'email'"
      (valueChange)="onEmailChange($event)">
    </app-form-input>
        <app-primary-button
            [text]="'Send Reset Email'"
            [type]="'button'"
            [loading]="isLoading()"
            [disabled]="!isValidForm()"
            (buttonClick)="onSubmit()" 
            >
        </app-primary-button>
        <div class="text-center mt-4">
          <a href="#" class="text-sm font-medium text-ivy-sky-blue no-underline hover:text-ivy-sky-blue-dark hover:underline" (click)="onBackToLogin()">
            Back to Login
          </a>
        </div>
      </div>
</app-auth-card>  