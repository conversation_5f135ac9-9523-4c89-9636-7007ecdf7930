// auth Card - Exact match to design
.auth-card {
  background: var(--ivy-base-white);
  border-radius: 16px;
  padding: 40px;
  width: 450px;
  max-width: 100%;  
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  position: relative;
}

// auth Header
.auth-header {
  margin-bottom: 32px;

  .auth-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ivy-neutral-800);
    margin: 0 0 8px 0;
    letter-spacing: -0.3px;
  }

  .auth-subtitle {
    font-size: 0.875rem;
    color: var(--ivy-neutral-600);
    margin: 0;
    font-weight: 400;
  }
}

// Card Content
.auth-card-content {
  display: flex;
  flex-direction: column;
}

// Responsive Design
@media (max-width: 768px) {
  .auth-card {
    padding: 32px 24px;
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 24px 20px;
    border-radius: 12px;
  }

  .auth-header {
    margin-bottom: 24px;

    .auth-title {
      font-size: 1.25rem;
    }
  }
}
