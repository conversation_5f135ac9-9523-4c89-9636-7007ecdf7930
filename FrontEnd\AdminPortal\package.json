{"name": "invofy-web", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:local": "ng serve --configuration local --port 4200", "start:dev": "ng serve --configuration dev --port 4201", "start:stage": "ng serve --configuration stage --port 4202", "start:prod": "ng serve --configuration production --port 3000", "build": "ng build", "build:local": "ng build --configuration development", "build:dev": "ng build --configuration local", "build:stage": "ng build --configuration stage", "build:prod": "ng build --configuration production", "deploy": "ng serve --host 0.0.0.0 --port 8000", "start:test": "ng serve --port 4300", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:invofy-web": "node dist/invofy-web/server/server.mjs"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/cdk": "^20.1.4", "@angular/common": "^20.1.5", "@angular/compiler": "^20.1.5", "@angular/core": "^20.1.5", "@angular/forms": "^20.1.5", "@angular/material": "^20.1.4", "@angular/platform-browser": "^20.1.5", "@angular/platform-server": "^20.1.5", "@angular/router": "^20.1.5", "@angular/ssr": "^20.1.4", "@tailwindcss/postcss": "^4.1.11", "express": "^5.1.0", "postcss": "^8.5.6", "rxjs": "~7.8.0", "tailwindcss": "^4.1.11", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.1.4", "@angular/cli": "^20.1.4", "@angular/compiler-cli": "^20.1.5", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}