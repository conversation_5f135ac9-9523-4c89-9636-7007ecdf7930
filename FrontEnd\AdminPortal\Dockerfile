ARG NODE_VERSION=22.14.0-alpine

FROM node:${NODE_VERSION} AS builder

WORKDIR /app

COPY package.json package-lock.json ./

RUN --mount=type=cache,target=/root/.npm npm ci

COPY . .

RUN npm run build:prod 

FROM node:${NODE_VERSION} AS runner

WORKDIR /app

RUN addgroup -S nodejs && adduser -S nodejs -G nodejs

COPY --chown=nodejs:nodejs --from=builder /app/dist/*/ .

USER nodejs

EXPOSE 3000  
CMD ["node", "./server/server.mjs"]