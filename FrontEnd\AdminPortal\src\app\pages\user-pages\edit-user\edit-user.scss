.edit-user-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px; 

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        color: #666;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
      }

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #333;
      }
    }

    .back-link {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--ivy-sky-blue, #2196f3);
      
      &:hover {
        background-color: rgba(33, 150, 243, 0.04);
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    p {
      margin-top: 16px;
      color: #666;
      font-size: 16px;
    }
  }

  .content-container {
    .user-tabs {
      .mat-mdc-tab-label {
        display: flex;
        align-items: center;
        gap: 8px;
        min-height: 48px;

        .verification-badge {
          font-size: 12px;
          color: #ff9800;
          font-weight: 500;
        }
      }

      .mat-mdc-tab-body-wrapper {
        padding-top: 24px;
      }
    }

    .tab-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    // Profile Tab Styles
    .profile-card {
      .profile-form {
        .form-row {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;

          .form-field {
            flex: 1;
          }
        }

        .password-section {
          margin-top: 24px;
          padding-top: 24px;
          border-top: 1px solid #e0e0e0;

          button {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
      }

      .card-actions {
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        padding: 16px 24px;
        border-top: 1px solid #e0e0e0;

        button {
          min-width: 100px;

          &[color="primary"] {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
      }
    }

    .metadata-card {
      .metadata-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .metadata-label {
          font-weight: 500;
          color: #666;
        }

        .metadata-value {
          color: #333;
          font-family: 'Courier New', monospace;
          font-size: 14px;
        }
      }
    }

    // Email Tab Styles
    .email-card {
      .email-form {
        .email-section {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 24px;

          .email-field {
            flex: 1;
          }

          button {
            margin-top: 8px;
            white-space: nowrap;
          }
        }
      }

      .verification-section {
        margin-top: 24px;
        padding: 20px;
        background-color: #fff8e1;
        border-radius: 8px;
        border-left: 4px solid #ff9800;

        .verification-alert {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 20px;

          .warning-icon {
            color: #ff9800;
            font-size: 24px;
            width: 24px;
            height: 24px;
            margin-top: 2px;
          }

          .alert-content {
            flex: 1;

            h4 {
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: #e65100;
            }

            p {
              margin: 0;
              color: #bf360c;
              line-height: 1.5;

              strong {
                font-weight: 600;
              }
            }
          }
        }

        .verification-form {
          .verification-input-section {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;

            .verification-field {
              flex: 1;
              max-width: 200px;
            }

            button {
              margin-top: 8px;
              white-space: nowrap;
            }
          }
        }
      }

      .policy-section {
        margin-top: 24px;
        padding: 16px;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .info-icon {
          color: #2196f3;
          font-size: 20px;
          width: 20px;
          height: 20px;
          margin-top: 2px;
        }

        .policy-content {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1565c0;
          }

          p {
            margin: 0;
            font-size: 14px;
            color: #0d47a1;
            line-height: 1.5;
          }
        }
      }
    }

    // MFA Tab Styles
    .mfa-card {
      .mfa-status {
        margin-bottom: 24px;

        .status-info {
          h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
          }

          .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;

            &.enabled {
              background-color: #e8f5e8;
              color: #2e7d32;
            }

            &.disabled {
              background-color: #ffebee;
              color: #d32f2f;
            }

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .mfa-actions {
        padding-top: 24px;

        .mfa-description {
          margin: 0 0 24px 0;
          color: #666;
          line-height: 1.6;
        }

        .action-buttons {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          button {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .edit-user-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-content {
        width: 100%;

        h1 {
          font-size: 20px;
        }
      }

      .back-link {
        align-self: flex-end;
      }
    }

    .content-container {
      .profile-card {
        .profile-form {
          .form-row {
            flex-direction: column;
            gap: 0;
          }
        }

        .card-actions {
          flex-direction: column;
          align-items: stretch;

          button {
            width: 100%;
          }
        }
      }

      .email-card {
        .email-form {
          .email-section {
            flex-direction: column;
            align-items: stretch;

            button {
              margin-top: 0;
              align-self: flex-start;
            }
          }
        }

        .verification-section {
          .verification-form {
            .verification-input-section {
              flex-direction: column;
              align-items: stretch;

              button {
                margin-top: 0;
                align-self: flex-start;
              }
            }
          }
        }
      }

      .mfa-card {
        .mfa-actions {
          .action-buttons {
            flex-direction: column;

            button {
              width: 100%;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}
