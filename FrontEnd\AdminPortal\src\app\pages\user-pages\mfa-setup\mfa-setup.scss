 .mfa-card {
   background: var(--ivy-base-white);
   border-radius: 16px;
   padding: 40px;
   max-width: 100%;
   box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
   position: relative;
 }

 ::ng-deep .mat-step-header {
   pointer-events: none !important;
 }

 .mfa-setup-container {
   display: flex;
   flex-direction: column;
   gap: 24px;
 }

 .auth-title {
   font-size: 1.5rem;
   font-weight: 600;
   color: var(--ivy-neutral-800);
   margin: 0 0 8px 0;
   letter-spacing: -0.3px;
 }

 // Messages
 .error-message,
 .success-message {
   display: flex;
   align-items: center;
   gap: 8px;
   padding: 12px 16px;
   border-radius: 8px;
   font-size: 0.875rem;

   mat-icon {
     font-size: 20px;
     width: 20px;
     height: 20px;
   }
 }

 .error-message {
   background-color: var(--ivy-error-50, #fef2f2);
   color: var(--ivy-error-700, #b91c1c);
   border: 1px solid var(--ivy-error-200, #fecaca);

   mat-icon {
     color: var(--ivy-error-600, #dc2626);
   }
 }

 .success-message {
   background-color: var(--ivy-success-50, #f0fdf4);
   color: var(--ivy-success-700, #15803d);
   border: 1px solid var(--ivy-success-200, #bbf7d0);

   mat-icon {
     color: var(--ivy-success-600, #16a34a);
   }
 }

 // Step content
 .step-content {
   .step-header {
     display: flex;
     align-items: center;
     gap: 12px;
     margin-bottom: 24px;

     .step-icon {
       font-size: 32px;
       width: 32px;
       height: 32px;
       color: var(--ivy-primary-600, #2563eb);
     }

     h3 {
       margin: 0;
       font-size: 1.25rem;
       font-weight: 600;
       color: var(--ivy-neutral-800, #1f2937);
     }
   }
 }

 // App recommendations
 .app-recommendations {

   .app-item {
     padding: 16px;
     border: 1px solid var(--ivy-neutral-400);
     border-radius: 8px;

   }


 }

 // QR section
 .qr-section {
   display: flex;
   flex-direction: column;
   align-items: center;
   gap: 24px;

   .qr-code-container {
     text-align: center;

     .qr-code {
       width: 200px;
       height: 200px;
       border: 1px solid var(--ivy-neutral-200, #e5e7eb);
       border-radius: 8px;
       margin-bottom: 12px;
     }

     p {
       font-size: 0.875rem;
       color: var(--ivy-neutral-600, #6b7280);
       margin: 0;
     }
   }

   .divider {
     position: relative;
     width: 100%;
     text-align: center;

     &::before {
       content: '';
       position: absolute;
       top: 50%;
       left: 0;
       right: 0;
       height: 1px;
       background-color: var(--ivy-neutral-200, #e5e7eb);
     }

     span {
       background-color: white;
       padding: 0 16px;
       font-size: 0.875rem;
       color: var(--ivy-neutral-500, #6b7280);
       position: relative;
       z-index: 1;
     }
   }

   .secret-key-container {
     text-align: center;
     width: 100%;

     p {
       font-size: 0.875rem;
       color: var(--ivy-neutral-700, #374151);
       margin-bottom: 12px;
     }

     .secret-key-display {
       display: flex;
       align-items: center;
       gap: 8px;
       padding: 12px 16px;
       background-color: var(--ivy-neutral-50, #f9fafb);
       border: 1px solid var(--ivy-neutral-200, #e5e7eb);
       border-radius: 8px;
       justify-content: center;

       code {
         font-family: 'Courier New', monospace;
         font-size: 0.875rem;
         color: var(--ivy-neutral-800, #1f2937);
         letter-spacing: 2px;
       }

       button {
         color: var(--ivy-primary-600, #2563eb);
       }
     }
   }
 }

 // Loading container
 .loading-container {
   display: flex;
   flex-direction: column;
   align-items: center;
   gap: 16px;
   padding: 40px 20px;

   p {
     font-size: 0.875rem;
     color: var(--ivy-neutral-600, #6b7280);
     margin: 0;
   }
 }

 // Verification section
 .verification-section {
   p {
     font-size: 0.875rem;
     color: var(--ivy-neutral-700, #374151);
     margin-bottom: 16px;
   }
 }

 // Action buttons
 .action-buttons {
   display: flex;
   gap: 12px;
   justify-content: flex-end;
   margin-top: 32px;

   .cancel-button,
   .back-button {
     color: var(--ivy-neutral-600, #6b7280);
   }
 }

 // Responsive design
 @media (max-width: 768px) {
   .progress-indicator {
     flex-direction: column;
     gap: 12px;

     .step-connector {
       display: none;
     }

     .step-indicator {
       flex-direction: row;
       text-align: left;
       width: 100%;

       .step-number {
         margin-bottom: 0;
         margin-right: 12px;
       }
     }
   }

   .action-buttons {
     flex-direction: column-reverse;
     gap: 8px;

     button {
       width: 100%;
     }
   }

   .qr-section .qr-code-container .qr-code {
     width: 160px;
     height: 160px;
   }
 }