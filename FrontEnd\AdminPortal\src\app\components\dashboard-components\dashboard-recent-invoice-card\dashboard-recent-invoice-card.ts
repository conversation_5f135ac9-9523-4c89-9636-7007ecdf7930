import { Component, input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import {MatChipsModule} from '@angular/material/chips';
@Component({
  selector: 'dashboard-recent-invoice-card',
  imports: [MatChipsModule,MatCardModule],
  templateUrl: './dashboard-recent-invoice-card.html',
  styleUrl: './dashboard-recent-invoice-card.scss'
})
export class DashboardRecentInvoiceCard {
  invoiceDate = input<string>('');
  invoiceNumber = input<string>('');
  name = input<string>('');
  status = input<string>('');
  invoiceAmount = input<string>('');
}
