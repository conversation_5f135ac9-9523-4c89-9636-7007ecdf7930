
.toast-container {
  display: flex;
  align-items: center;
  min-width: 300px;
  max-width: 500px; 
  border-radius: 8px; 
  overflow: hidden;

  .toast-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    gap: 12px;
  }

  .toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .toast-icon-symbol {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  .toast-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
  }

  .toast-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;

    .toast-action-button {
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      min-width: auto;
      padding: 4px 8px;
      height: 32px;
    }

    .toast-close-button {
      width: 32px;
      height: 32px;
      
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  // Success Toast
  &.toast-success {
    background-color: #4caf50;
    color: white;

    .toast-icon-symbol {
      color: white;
    }

    .toast-action-button {
      color: white;
      border-color: rgba(255, 255, 255, 0.3);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .toast-close-button {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  // Error Toast
  &.toast-error {
    background-color: #f44336;
    color: white;

    .toast-icon-symbol {
      color: white;
    }

    .toast-action-button {
      color: white;
      border-color: rgba(255, 255, 255, 0.3);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .toast-close-button {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  // Warning Toast
  &.toast-warning {
    background-color: #ff9800;
    color: white;

    .toast-icon-symbol {
      color: white;
    }

    .toast-action-button {
      color: white;
      border-color: rgba(255, 255, 255, 0.3);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .toast-close-button {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  // Info Toast
  &.toast-info {
    background-color: var(--ivy-sky-blue, #2196f3);
    color: white;

    .toast-icon-symbol {
      color: white;
    }

    .toast-action-button {
      color: white;
      border-color: rgba(255, 255, 255, 0.3);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .toast-close-button {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .toast-container {
    min-width: 280px;
    max-width: 90vw;

    .toast-content {
      padding: 10px 12px;
      gap: 8px;
    }

    .toast-message {
      font-size: 13px;
    }

    .toast-actions {
      .toast-action-button {
        font-size: 11px;
        padding: 2px 6px;
        height: 28px;
      }

      .toast-close-button {
        width: 28px;
        height: 28px;
        
        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

// Animation overrides for Material Snackbar
::ng-deep {
  .mat-mdc-snack-bar-container {
    &.toast-success-container {
      --mdc-snackbar-container-color: #4caf50;
    }

    &.toast-error-container {
      --mdc-snackbar-container-color: #f44336;
    }

    &.toast-warning-container {
      --mdc-snackbar-container-color: #ff9800;
    }

    &.toast-info-container {
      --mdc-snackbar-container-color: var(--ivy-sky-blue, #2196f3);
    }
  }
}
