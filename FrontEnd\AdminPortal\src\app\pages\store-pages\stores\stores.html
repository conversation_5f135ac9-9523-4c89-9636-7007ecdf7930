<div class="page-container">
    <!-- Header Section -->
    <div class="header-section">
        <div class="header-left">
            <h2 class="page-count">Stores ({{ totalRecords() }})</h2>
        </div>
        <div class="header-right">
            <app-table-search-input buttonClasses="!h-14 rounded-full" iconName="search" appearance="outline"
                placeholder="Search Store" [value]="searchText()" (input)="onSearchChange($event)"
                class="search-input !h-14 rounded-full" />

            <button mat-raised-button color="primary" class="add-btn !text-ivy-base-white !bg-ivy-dark-blue !h-14"
                (click)="onAddStore()">
                <mat-icon class="material-symbols">add</mat-icon>
                Add New Store
            </button>
        </div>
    </div>

    <!-- Error Message -->
    @if(errorMessage()) {
    <div class="error-message">
        <mat-icon class="material-symbols">error</mat-icon>
        <span>{{ errorMessage() }}</span>
    </div>
    }

    <!-- Loading Spinner -->
    @if(isLoading()) {
    <div class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading stores...</p>
    </div>
    }

    <!-- Stores Table -->
    @if(!isLoading()) {
    <div class="table-container">
    <table mat-table [dataSource]="dataSource" class="data-table" multiTemplateDataRows>

    <!-- Store Name Column -->
    <ng-container matColumnDef="storeName">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let store" class="store-name-cell" >
            <div class="store-info">
                <mat-icon class="material-symbols expand-icon"
                        [class.rotated]="expandedElement.includes(store)">
                    chevron_right
                </mat-icon>
                <div class="store-name">{{ store.storeName }}</div>
            </div>
        </td>
    </ng-container>

    <!-- Owner Column -->
    <ng-container matColumnDef="owner">
        <th mat-header-cell *matHeaderCellDef>Owner</th>
        <td mat-cell *matCellDef="let store">{{ store.owner }}</td>
    </ng-container>

    <!-- Email Column -->
    <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef>Email</th>
        <td mat-cell *matCellDef="let store">{{ store.email }}</td>
    </ng-container>

    <!-- Phone Column -->
    <ng-container matColumnDef="phone">
        <th mat-header-cell *matHeaderCellDef>Phone</th>
        <td mat-cell *matCellDef="let store">{{ formatPhoneNumber(store.phone) }}</td>
    </ng-container>

    <!-- Location Column -->
    <ng-container matColumnDef="location">
        <th mat-header-cell *matHeaderCellDef>Location</th>
        <td mat-cell *matCellDef="let store">{{ store.location }}</td>
    </ng-container>

    <!-- Progress Status Column -->
    <ng-container matColumnDef="progressStatus">
        <th mat-header-cell *matHeaderCellDef>Progress Status</th>
        <td mat-cell *matCellDef="let store">
            <span [class]="getProgressStatusClass(store.progressStatus)">{{ store.progressStatus }}</span>
        </td>
    </ng-container>

    <!-- Created Date Column -->
    <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef>Created Date</th>
        <td mat-cell *matCellDef="let store">{{ formatDate(store.createdDate) }}</td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let store">
            <button mat-icon-button [matMenuTriggerFor]="actionsMenu"
                    [matMenuTriggerData]="{store: store}"
                    class="actions-button">
                <mat-icon class="material-symbols">more_vert</mat-icon>
            </button>

            <!-- Actions Menu Template -->
            <mat-menu #actionsMenu="matMenu" class="store-actions-menu">
                <ng-template matMenuContent let-store="store">
                    <!-- View Action -->
                    <button mat-menu-item (click)="onViewStore(store)" class="menu-item-view">
                        <mat-icon class="material-symbols menu-icon">visibility</mat-icon>
                        <span>View Details</span>
                    </button>

                    <!-- Edit Action -->
                    <button mat-menu-item (click)="onEditStore(store)" class="menu-item-edit">
                        <mat-icon class="material-symbols menu-icon">edit</mat-icon>
                        <span>Edit</span>
                    </button>
                </ng-template>
            </mat-menu>
        </td>
    </ng-container>

    <!-- Expanded Detail Column -->
    <ng-container matColumnDef="expandedDetail">
        <td mat-cell *matCellDef="let store" [attr.colspan]="displayedColumns.length">
            
            <div class="expanded-content" [class.show]="expandedElement.includes(store)">
                <div  class="flex justify-around">
                    <div class="grid gap-2" >
                        <div class="detail-item"><span class="font-bold">Owner: </span>{{ store.owner }} </div>
                        <div class="detail-item"><span class="font-bold">Phone:</span>  {{store.phone }} </div>
                        <div class="detail-item"> <span class="font-bold">Email: </span>{{ store.email }} </div>

                        
                    </div> 
                    <div class="grid gap-2" >
                        <div class="detail-item"> <span class="font-bold">Store Tax:</span> {{ formatStoreTax(store.storeTax) }} </div>
                        <div class="detail-item"><span class="font-bold">Vendors:  </span>{{ store.vendors}} </div>
                        <div class="detail-item"> <span class="font-bold">Items: </span>{{ store.items}}</div>
                        
                    </div> 
                    <div class="grid gap-2">
                        <div class="detail-item"><span class="font-bold"> Store Hours: </span>{{ store.storeHours }}</div>
                        <div class="detail-item"> <span class="font-bold"> Location: </span> {{ store.location }}</div>
                        
                    </div> 
                </div>
            </div>
        </td>
    </ng-container>

    <!-- Header Row -->
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>

    <!-- Main Data Row -->
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
        class="element-row"
        (click)="toggleRow(row)">
    </tr>

    <!-- Expanded Detail Row -->
    <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="detail-row"></tr>

</table>


        @if(dataSource.data.length === 0 && !isLoading()) {
        <div class="no-data-container">
            <mat-icon class="material-symbols no-data-icon">store</mat-icon>
            <h3>No stores found</h3>
            <p>{{ searchText() ? 'No stores match your search criteria.' : 'No stores have been created yet.' }}</p>
        </div>
        }
    </div>

    @if(totalRecords() > 0) {
    <mat-paginator [length]="totalRecords()" [pageSize]="pageSize()" [pageIndex]="pageIndex()"
        [pageSizeOptions]="[5, 10, 25, 50]" (page)="onPageChange($event)" showFirstLastButtons
        aria-label="Select page of stores">
    </mat-paginator>
    }
    }
</div>
