 <mat-card class=" flex-grow  p-3 !border-none grid gap-2" appearance="outlined">
        <mat-card-header>
        <mat-card-title class=" !text-ivy-dark-blue !text-4xl !font-bold">
            {{title()}}
        </mat-card-title>
        @if (tagText()) {
         <mat-basic-chip class="bg-ivy-light-white rounded-lg px-3 py-2 text-ivy-dark-blue text-xs size-fit font-medium top-3 right-3 !absolute">{{tagText()}}</mat-basic-chip>
        }
        </mat-card-header>
        <mat-card-content>
        <p class="!text-ivy-dark-blue">
            {{body()}}
        </p>
        </mat-card-content> 
</mat-card>